mstar_env = "QA-DR"
mstar_region = "aws-us-west-2"
aws_vpc = "vpc-0ac38b7b0189f34f0"
aws_security_groups = [
  "console",
  "private_app",
  "private_db",
  "public",
  "private_active_directory_client",
  "private_app_non-prod_outposts",
  "private_os_services_non-prod_outposts",
  "private_do_web_non-prod_outposts",
  "private_db_non-prod_outposts",
  "private_internet_outpost",
  "private_do_common_non-prod_outposts",
]
aws_availability_zone = [
  "us-west-2a",
  "us-west-2b",
  "us-west-2c",
]
default_elasticache_security_group = [
  "console",
  "private_app",
]
default_ec2_security_group = [
  "console",
  "private_app",
]
default_lb_security_group = [
  "private_app",
]
public_lb_security_group = [
  "public",
]

default_outposts_security_group = [
  "do_web_server",
  "private_app_non-prod_outposts",
  "private_os_services_non-prod_outposts",
  "private_do_web_non-prod_outposts",
  "private_internet_outpost",
  "private_do_common_non-prod_outposts",
  "private_dcs_non-prod_outposts"
]

default_tscache_outposts_security_group = [
  "do_web_server",
  "private_app_non-prod_outposts",
  "private_os_services_non-prod_outposts",
  "private_do_web_non-prod_outposts",
  "private_db_non-prod_outposts",
  "private_internet_outpost",
  "private_do_common_non-prod_outposts",
  "private_dcs_non-prod_outposts"
]

aws_zone_name_dataapi = "dod2af5.easn.morningstar.com."
