variable s3_replication_bucket {
  default = ""
}

variable s3_replication_bucket_prefix {
  default = "dr-"
}

variable s3_replication_role {
  default = "s3-replication"
}

variable dr_replication_key_pas {
  default = false
}

locals {
  _s3_replication_bucket = length(var.s3_replication_bucket_prefix) > 0 && !module.common-prefix.isDR ? trimspace(substr(format("%s%s%s                                                                ", module.common-prefix.env-, var.s3_replication_bucket_prefix, local.name), 0, 63)):""
  s3_replication_bucket = length(var.s3_replication_bucket) > 0? var.s3_replication_bucket: local._s3_replication_bucket
  enable_replication = local.enable_bucket && length(local.s3_replication_bucket) > 0 && length(var.s3_replication_role) > 0
  replica_arn = local.enable_replication? "arn:aws:s3:::${local.s3_replication_bucket}":""
  replica_id = local.enable_replication? local.s3_replication_bucket:""
  replication_role_arn = local.enable_replication? data.aws_iam_role.replication-role[0].arn:""
  replication_key_arn = local.enable_replication && !module.common-prefix.isDR? data.aws_kms_alias.replication_key[0].target_key_arn:local.dr_replication_key
  dr_replication_key = var.dr_replication_key_pas? data.aws_kms_alias.replication_key_dr[0].target_key_arn:""
}

output replica_arn {
  value = local.replica_arn
}

output replication_key_arn {
  value = local.replication_key_arn
}

data aws_iam_role replication-role {
  count = local.enable_replication? 1:0
  name = var.s3_replication_role
}
