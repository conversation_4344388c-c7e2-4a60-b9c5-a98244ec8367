module "common-iam-s3-replication" {
  source = "../../modules/iam/resource"
  enable_role = var.enable_role_s3_replication
  name = "s3-replication"
  mstar_env = var.mstar_env
  use_env_prefix = false
  use_role_name_prefix = false
  service_prefix = ""
  assume_role_policy = module.common-iam-const.assume-role-policy-s3
  iam_role_tags = module.esg-tags.default_iam_role_tags
}

variable "enable_role_s3_replication" {
  default = false
}
