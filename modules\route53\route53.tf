data "aws_route53_zone" "do-public" {
  name = var.aws_zone_name
  private_zone = false
}

variable "aws_zone_name" {}

resource "aws_route53_record" "www" {
  for_each        = var.name_ips
  allow_overwrite = true
  zone_id         = data.aws_route53_zone.do-public.zone_id
  name            = replace(each.key,"_","-")
  type            = "A"
  ttl             = "300"
  records         = [each.value]
}

variable name_ips {
  type = map(any)
}