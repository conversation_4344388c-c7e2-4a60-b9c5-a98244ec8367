module "common-ecs-ec2" {
  source = "../../modules/ec2/resource"
  mstar_env = var.mstar_env
  name = local.name
  availability_zones = module.common-vpc.default_ec2_availability_zones
  security_groups = module.common-vpc-sg.default_ec2_security_group

  subnet_ids = module.common-vpc.default_private_subnet_ids
  initial_capacity = var.calc-ec2_asg_desired
  max_size = var.calc-ec2_asg_max
  min_size = var.calc-ec2_asg_min
  enable_scaling = false
  use_asg_name_prefix = false
  asg_tags = var.asg_tags
  key_name = "s3accssorkeypair"
  launch_template_tags = module.pcs-tags.default_launch_template_tags
  aws_instance_type = var.aws_instance_type
  user_data = module.common-ec2-userdata-ecs.user_data
  aws_instance_profile = local.aws_instance_profile
}

resource "aws_ecs_service" "poc-ads-worker-dcs" {
  name = "${module.common-prefix.env-}${local.name}"
  cluster = "${module.common-prefix.env-}${local.cluster_name}"
  launch_type = "EC2"
  task_definition = aws_ecs_task_definition.poc-ads-worker-dcs-task.arn
  scheduling_strategy = "DAEMON"
  placement_constraints {
    type = "memberOf"
    expression = "attribute:service == ${local.name}"
  }

  network_configuration {
    security_groups = module.common-vpc-sg.default_ec2_security_group
    subnets = module.common-vpc.default_private_subnet_ids
  }
}


module "common-ec2-userdata-ecs" {
  source = "../../modules/ec2/userdata/ecs"
  mstar_env = var.mstar_env
  name = local.name
  cluster = local.cluster_name
}

locals {
  name = "dcs-ads-worker-asg"
  cluster_name = "do-ads-dcs"
  aws_instance_profile = "ecsInstanceWithSSM"
}

variable "aws_instance_type" {
default = "t3.large"
}

variable "calc-ec2_asg_max" {
default = 5
}

variable "calc-ec2_asg_min" {
default = 0
}

variable "calc-ec2_asg_desired" {
default = 0
}



