variable enable_scaling_policy {
  default = false
}

resource aws_autoscaling_policy policy-scale-out {
  count = local.enable_target_tracking_policy? 1:0
  autoscaling_group_name = local.asg_name
  name = "${module.common-prefix.env-prefix-}scale-out"
  policy_type = "TargetTrackingScaling"
  target_tracking_configuration {
    target_value = var.scale_out_policy_target_cpu_utilization
    disable_scale_in = !var.enable_target_tracking_policy_scale_in
    predefined_metric_specification {
      predefined_metric_type = "ASGAverageCPUUtilization"
    }
  }
}

variable enable_target_tracking_policy_scale_in {
  default = false
}

variable scale_out_policy_target_cpu_utilization {
  default = 80
}

resource aws_autoscaling_policy policy-scale-in {
  count = local.enable_scale_in_policy? 1:0
  autoscaling_group_name = local.asg_name
  name = "${module.common-prefix.env-prefix-}scale-in"
  policy_type = "StepScaling"
  adjustment_type = "ChangeInCapacity"
  step_adjustment {
    metric_interval_upper_bound = 0
    scaling_adjustment = -1
  }
}

locals {
  enable_target_tracking_policy = var.enable_scaling_policy && var.enable_scaling
  enable_scale_in_policy = var.enable_scaling_policy && var.enable_scaling
  scale_in_policy_arns = local.enable_scale_in_policy? aws_autoscaling_policy.policy-scale-in[*].arn: []
}

output scale_in_policy_arns {
  value = local.scale_in_policy_arns
}

resource aws_cloudwatch_metric_alarm CPU-le-threshold {
  count = local.enable_scale_in_policy && var.enable_cpu_le_threshold_alarm? 1:0
  alarm_name = "${module.common-prefix.env-prefix} CPU below threshold"
  comparison_operator = "LessThanOrEqualToThreshold"
  namespace = "AWS/EC2"
  metric_name = "CPUUtilization"
  statistic = "Average"
  period = 300
  evaluation_periods = 2
  threshold = var.scale_in_alarm_cpu_threshold
  dimensions = {
    AutoScalingGroupName = local.asg_name
  }
  alarm_actions = local.scale_in_policy_arns
  tags = var.cloudwatch_metric_alarm_tags
}

variable enable_cpu_le_threshold_alarm {
  default = false
}

variable scale_in_alarm_cpu_threshold {
  default = 1
}
