
# Terraform scripts for DO

$ rm -f README.md; find modules -name README.md | sort | while read line; do echo '#' ${line%%/README.md} >> README.md; cat $line >> README.md; echo '' >> README.md; done

# modules
common modules for terraform 0.12

# modules/ami/linux2
Resolve Morningstar AMI
# modules/ami/linux2_ecs
Resolve Morningstar AMI (ECS optimized)
# modules/api/resource
Borrowed code from https://msstash.morningstar.com/projects/MAAS/repos/terraform/browse/api_gateway/modules

# modules/batch/resource
create a job queue. Currently compute environment is read-only so it is better to use the same root module (50-batch) and upgrade at once. In future maybe two modules can be created to rotate without downtime.

# modules/batch/resource/job_definition
create a job definition. A typical use case is to run the same Java artifact with different profiles, each registers as a job definition. A root module usually registers multiple job definition resource that shares the same artifact version.

# modules/caller
current account id

# modules/ec2/resource
create asg and launch template. When an ECS service runs in DAEMON mode, this module can also setup autoscaling based on CPU usage target

# modules/ec2/userdata
EC2 userdata that has boothook
# modules/ec2/userdata/const
EC2 userdata to install packages
# modules/ec2/userdata/ecs
EC2 userdata to associate ECS service to an instance
# modules/ecs/ecr
format ECR image path

# modules/ecs/logs
format ECS log path

# modules/ecs/resource/ecr/policy
ECR image policy

# modules/ecs/resource/service
ECS service that runs on EC2

# modules/ecs/resource/service/task
ECS task that runs on EC2

# modules/ecs/resource/service_awsvpc
ECS service that runs on FARGATE

# modules/ecs/resource/service_awsvpc/task
ECS task that runs on FARGATE

# modules/ecs/resource/sqs
one typical ECS/batch application is built around SQSHandler, that consumes SQS messages and throw errors (and retry) when anything goes wrong. This module create two SQS queues, one is DLQ, to simplify the setup.

# modules/iam/assume-role-policy
create assume-role policy

# modules/iam/const
often used assume-role policy and managed role policies

# modules/iam/elastisearch-policies
policies applicable to elastisearch resource and actions

# modules/iam/kms-policies
policies applicable to kms resource and actions

# modules/iam/logs-policies
policies applicable to logs resource and actions

# modules/iam/policies
policies for creating IAM role policies

# modules/iam/resource
create role with service prefix and name prefix

# modules/iam/s3-policies
policies applicable to s3 resource and actions

# modules/iam/sns-policies
policies applicable to SNS resource and actions

# modules/iam/sqs-policies
policies applicable to SQS resource and actions

# modules/java
format java-options. One often used output variables are "java-options" and "gc-summary"

# modules/java/kafka_options
format kafka required java-options and environments

# modules/java/operation_log_options
format oplogs path

# modules/kms
const reference. Make sure all consts here are created in all environments and regions (STG, STG-DR, PROD, DR) that an ECS/batch application is deployed. TBD make it a link based one because refreshing takes time.

# modules/lb/resource
create LB with domain name and HTTPS listener

# modules/prefix
format names

# modules/s3
format bucket id and resolve ARN

# modules/s3/alias
const reference for historical reason

# modules/s3/bucket_files_v2
format list of ARNs to use in role policy

# modules/s3/resource
create bucket

# modules/s3/resource/replication
create bucket with replication

# modules/s3/resource/replication/encrypted
create bucket with replication and encryption

# modules/s3_const
const reference. This module is not imported, but individual files are linked when necessary.

Each bucket should appear in only one file to avoid conflicts. Files starts with 's3p' are not only buckets but also comes with prefix.

# modules/sqs/resource
create SQS queue

# modules/tags
format tags to help remember different tag formats

# modules/vpc
resolve subnet ids

# modules/vpc/sg
resolve security group ids

