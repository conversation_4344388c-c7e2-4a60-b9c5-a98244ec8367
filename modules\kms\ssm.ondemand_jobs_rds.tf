data aws_ssm_parameter ondemand-jobs-rds-password {
  count = length(local.ondemand-jobs-rds-password)
  name = local.ondemand-jobs-rds-password[count.index]
}

output para-ondemand-jobs-rds-password {
  value = data.aws_ssm_parameter.ondemand-jobs-rds-password[*].arn
}

locals {
  ondemand-jobs-rds-password = split(",", module.common-prefix.isPROD? var.prod-ondemand-jobs-rds-password: module.common-prefix.isDR? var.nonprod-ondemand-jobs-rds-password[0]: join(",", var.nonprod-ondemand-jobs-rds-password))
}

variable prod-ondemand-jobs-rds-password {
  default = "ondemand-jobs-rds-password"
}

variable nonprod-ondemand-jobs-rds-password {
  type = list(string)
  default = [
    "ondemand-jobs-rds-password",
  ]
}
