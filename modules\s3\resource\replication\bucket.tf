module common-prefix {
  source = "../../../prefix"
  mstar_env = var.mstar_env
}

variable mstar_env {
}

resource aws_s3_bucket_public_access_block no_replication {
  count = local.enable_bucket && !local.enable_replication? 1:0
  bucket = aws_s3_bucket.no_replication[0].id
  block_public_acls = true
  block_public_policy = true
  ignore_public_acls = true
  restrict_public_buckets = true
  depends_on = [
    aws_s3_bucket.no_replication
  ]
}

resource aws_s3_bucket no_replication {
  count = local.enable_bucket && !local.enable_replication? 1:0
  bucket = "${module.common-prefix.env-}${local.name}"
  tags = var.s3_bucket_tags
  versioning {
    enabled = true
  }
  dynamic lifecycle_rule {
    for_each = local.lifecycle_rules
    iterator = _
    content {
      enabled = _.value.enabled
      prefix = _.value.prefix
      expiration {
        days = _.value.expiration.days
        expired_object_delete_marker = _.value.expiration.expired_object_delete_marker
      }
      noncurrent_version_expiration {
        days = _.value.noncurrent_version_expiration.default_nonconcurrent_version_expiration
      }
    }
  }
}

resource aws_s3_bucket_public_access_block bucket {
  count = local.enable_bucket && local.enable_replication? 1:0
  bucket = aws_s3_bucket.bucket[0].id
  block_public_acls = true
  block_public_policy = true
  ignore_public_acls = true
  restrict_public_buckets = true
  depends_on = [
    aws_s3_bucket.bucket
  ]
}

resource aws_s3_bucket bucket {
  count = local.enable_bucket && local.enable_replication? 1:0
  bucket = "${module.common-prefix.env-}${local.name}"
  tags = var.s3_bucket_tags
  versioning {
    enabled = true
  }
  dynamic lifecycle_rule {
    for_each = local.lifecycle_rules
    iterator = _
    content {
      enabled = _.value.enabled
      prefix = _.value.prefix
      expiration {
        days = _.value.expiration.days
        expired_object_delete_marker = _.value.expiration.expired_object_delete_marker
      }
      noncurrent_version_expiration {
        days = _.value.noncurrent_version_expiration.default_nonconcurrent_version_expiration
      }
    }
  }
  replication_configuration {
    role = local.replication_role_arn
    rules {
      id = local.replica_arn
      destination {
        bucket = local.replica_arn
      }
      prefix = ""
      status = local.enable_replication? "Enabled":"Disabled"
    }
  }
}

module "common-s3-lifecycle_rule" {
  source = "../../lifecycle_rule"
}

locals {
  lifecycle_rules = distinct(concat(list(module.common-s3-lifecycle_rule.rule), var.lifecycle_rules))
}

variable lifecycle_rules {
  type = list(object({
    prefix = string
    enabled = bool
    expiration = object({
      days = number
      expired_object_delete_marker = bool
    })
    noncurrent_version_expiration = object({
      default_nonconcurrent_version_expiration = number
    })
  }))
  default = []
}

variable name {
}

variable name_prefix {
  default = "mstar-dwm-"
}

variable qa_catchall {
  default = true
}

variable s3_bucket_tags {
  type = map(string)
}

locals {
  catchall_enabled = var.qa_catchall && (module.common-prefix.ENV == "QA" || module.common-prefix.ENV == "DEV")
  enable_bucket = !local.catchall_enabled
  resource = local.enable_replication? aws_s3_bucket.bucket: aws_s3_bucket.no_replication
  resource_arn = local.resource[0].arn
  resource_id = local.resource[0].id
}

output arn {
  value = local.resource_arn
}

output id {
  value = local.resource_id
}

locals {
  name = "${var.name_prefix}${var.name}"
}

output enable_bucket {
  value = local.enable_bucket
}
