{
  "Effect": "Allow",
  "Action": [
    "s3:GetObjectVersionForReplication",
    "s3:GetObjectVersionAcl",
    "s3:GetObjectVersionTagging"
  ],
  "Resource": "BEGIN{}${bucket_files}END{}"
},
{
  "Effect": "Allow",
  "Action": [
    "s3:ListBucket",
    "s3:GetReplicationConfiguration"
  ],
  "Resource": "BEGIN{}${buckets}END{}"
},
{
  "Effect": "Allow",
  "Action": [
    "s3:ReplicateObject",
    "s3:ReplicateDelete",
    "s3:ReplicateTags"
  ],
  "Resource": "BEGIN{}${replication_bucket_files}END{}"
}
