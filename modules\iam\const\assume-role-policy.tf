module common-iam-assume-role-policy-batch {
  source = "../assume-role-policy"
  services = [
    "batch.amazonaws.com",
  ]
}

output assume-role-policy-batch {
  value = module.common-iam-assume-role-policy-batch.assume-role-policy
}

module common-iam-assume-role-policy-application-autoscaling {
  source = "../assume-role-policy"
  services = [
    "application-autoscaling.amazonaws.com",
  ]
}

output assume-role-policy-application-autoscaling {
  value = module.common-iam-assume-role-policy-application-autoscaling.assume-role-policy
}

module common-iam-assume-role-policy-ec2 {
  source = "../assume-role-policy"
  services = [
    "ec2.amazonaws.com",
  ]
}

output assume-role-policy-ec2 {
  value = module.common-iam-assume-role-policy-ec2.assume-role-policy
}

module common-iam-assume-role-policy-ecs-tasks {
  source = "../assume-role-policy"
  services = [
    "ecs-tasks.amazonaws.com",
  ]
}

output assume-role-policy-ecs-tasks {
  value = module.common-iam-assume-role-policy-ecs-tasks.assume-role-policy
}

module common-iam-assume-role-policy-events {
  source = "../assume-role-policy"
  services = [
    "events.amazonaws.com",
  ]
}

output assume-role-policy-events {
  value = module.common-iam-assume-role-policy-events.assume-role-policy
}

module common-iam-assume-role-policy-glue {
  source = "../assume-role-policy"
  services = [
    "glue.amazonaws.com",
  ]
}

output assume-role-policy-glue {
  value = module.common-iam-assume-role-policy-glue.assume-role-policy
}

module common-iam-assume-role-policy-lambda {
  source = "../assume-role-policy"
  services = [
    "lambda.amazonaws.com",
  ]
}

output assume-role-policy-lambda {
  value = module.common-iam-assume-role-policy-lambda.assume-role-policy
}

module common-iam-assume-role-policy-s3 {
  source = "../assume-role-policy"
  services = [
    "s3.amazonaws.com",
  ]
}

output assume-role-policy-s3 {
  value = module.common-iam-assume-role-policy-s3.assume-role-policy
}

module common-iam-assume-role-policy-sagemaker {
  source = "../assume-role-policy"
  services = [
    "sagemaker.amazonaws.com",
  ]
}

output assume-role-policy-sagemaker {
  value = module.common-iam-assume-role-policy-sagemaker.assume-role-policy
}

module common-iam-assume-role-policy-spotfleet {
  source = "../assume-role-policy"
  services = [
    "spotfleet.amazonaws.com",
  ]
}

output assume-role-policy-spotfleet {
  value = module.common-iam-assume-role-policy-spotfleet.assume-role-policy
}
