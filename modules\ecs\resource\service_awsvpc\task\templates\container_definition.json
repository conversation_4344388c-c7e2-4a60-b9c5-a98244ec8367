[{"name": "${name}", "image": "${image}", "cpu": "BEGIN{}${cpu}END{}", "memoryReservation": "BEGIN{}${memoryReservation}END{}", "portMappings": "BEGIN{}${portMappings}END{}", "ulimits": "BEGIN{}${ulimits}END{}", "essential": true, "readonlyRootFilesystem": false, "environment": "BEGIN{}${environment}END{}", "linuxParameters": "BEGIN{}${linuxParameters}END{}", "mountPoints": [], "volumesFrom": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "${awslogs-group}", "awslogs-region": "${awslogs-region}", "awslogs-stream-prefix": "${awslogs-stream-prefix}"}}}]