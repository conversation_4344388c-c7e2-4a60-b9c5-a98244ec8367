param(
    [Parameter(Mandatory=$true)][<PERSON><PERSON>("e")][string]$Environment,
    [Parameter(Mandatory=$true)][<PERSON><PERSON>("s")][string]$Service,
    [<PERSON><PERSON>("d")][switch]$Destroy = $false,
    [<PERSON><PERSON>("r")][switch]$Redeploy = $false
)

# Get the directory where this script is located
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Definition

$ServicePaths = @{
    "fcgi" = Join-Path $ScriptDir "20-dcs-ec2-fcgi"
    "scheduler" = Join-Path $ScriptDir "21-dcs-ec2-scheduler"
    "memcache" = Join-Path $ScriptDir "22-dcs-ec2-memcache"
    "ads" = Join-Path $ScriptDir "10-dcs-ec2-ads"
    "attribution" = Join-Path $ScriptDir "11-dcs-ec2-attribution"
    "assetallocation" = Join-Path $ScriptDir "13-dcs-ec2-assetallocation"
}
if (-not $ServicePaths.ContainsKey($Service)) {
    Write-Error "Invalid service: $Service, valid services are $($ServicePaths.Keys -join ', ')"
    exit 1
}

$ServicePath = $ServicePaths[$Service]
$BackendConfigFile = "terraform.$($Environment.ToUpper()).tfbackend"
$VarFile = "terraform.$($Environment.ToUpper()).tfvars"

if (-not (Test-Path (Join-Path $ServicePath $BackendConfigFile))) {
    Write-Error "Invalid backend config file: $(Join-Path $ServicePath $BackendConfigFile)"
    exit 1
}
Write-Host "Using backend config file: $(Join-Path $ServicePath $BackendConfigFile)"
if (-not (Test-Path (Join-Path $ServicePath $VarFile))) {
    Write-Error "Invalid variable file: $(Join-Path $ServicePath $VarFile)"
    exit 1
}
Write-Host "Using variable file: $(Join-Path $ServicePath $VarFile)"

terraform -chdir="$ServicePath" init --reconfigure --upgrade -backend-config="$BackendConfigFile"
if ($Redeploy) {
    terraform -chdir="$ServicePath" destroy -var-file="$VarFile" -auto-approve
    terraform -chdir="$ServicePath" apply -var-file="$VarFile" -auto-approve
} elseif ($Destroy) {
    terraform -chdir="$ServicePath" destroy -var-file="$VarFile" -auto-approve
} else {
    terraform -chdir="$ServicePath" apply -var-file="$VarFile" -auto-approve
}