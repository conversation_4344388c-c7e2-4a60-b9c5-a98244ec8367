param(
    [Parameter(Mandatory=$true)][<PERSON><PERSON>("e")][string]$Environment,
    [Parameter(Mandatory=$true)][<PERSON><PERSON>("s")][string]$Service,
    [<PERSON><PERSON>("d")][switch]$Destroy = $false,
    [<PERSON><PERSON>("r")][switch]$Redeploy = $false
)

$ServicePaths = @{
    "fcgi" = "20-dcs-ec2-fcgi"
    "scheduler" = "21-dcs-ec2-scheduler"
    "memcache" = "22-dcs-ec2-memcache"
    "ads" = "10-dcs-ec2-ads"
    "attribution" = "11-dcs-ec2-attribution"
    "assetallocation" = "13-dcs-ec2-assetallocation"
}
if (-not $ServicePaths.ContainsKey($Service)) {
    Write-Error "Invalid service: $Service, valid services are $($ServicePaths.Keys -join ', ')"
    exit 1
}

$ServicePath = $ServicePaths[$Service]
$BackendConfigFile = "terraform.$($Environment.ToUpper()).tfbackend"
$VarFile = "terraform.$($Environment.ToUpper()).tfvars"
if (-not (Test-Path "$ServicePath/$BackendConfigFile")) {
    Write-Error "Invalid backend config file: $BackendConfigFile"
    exit 1
}
Write-Host "Using backend config file: $BackendConfigFile"
if (-not (Test-Path "$ServicePath/$VarFile")) {
    Write-Error "Invalid variable file: $VarFile"
    exit 1
}
Write-Host "Using variable file: $VarFile"

terraform -chdir="$ServicePath" init --reconfigure --upgrade -backend-config="$BackendConfigFile"
if ($Redeploy) {
    terraform -chdir="$ServicePath" destroy -var-file="$VarFile" -auto-approve
    terraform -chdir="$ServicePath" apply -var-file="$VarFile" -auto-approve
} elseif ($Destroy) {
    terraform -chdir="$ServicePath" destroy -var-file="$VarFile" -auto-approve
} else {
    terraform -chdir="$ServicePath" apply -var-file="$VarFile" -auto-approve
}