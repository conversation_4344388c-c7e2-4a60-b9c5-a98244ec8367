data aws_vpc default {
  id = var.aws_vpc
}

variable aws_vpc {
}

output default_vpc {
  value = data.aws_vpc.default.id
}

data aws_subnets private_subnet {
  filter{
    name = "vpc-id"
    values = [data.aws_vpc.default.id]
  }
  tags = {
    FUNCTION = "PRIVATE"
  }
}

output default_private_subnet_ids {
  value = sort(data.aws_subnets.private_subnet.ids)
}

data aws_subnets public_subnet {
  filter{
    name = "vpc-id"
    values = [data.aws_vpc.default.id]
  }
  tags = {
    FUNCTION = "PUBLIC"
  }
}

output default_public_subnet_ids {
  value = sort(data.aws_subnets.public_subnet.ids)
}