#!/usr/bin/env bash

#echo "\#(o_o)#/"

sudo su

echo "start to build dcs-worker-pln env:${base-env}">> /tmp/install.log

echo "update time zone...">> /tmp/install.log
export TZ=America/Chicago
ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

s3bucket="dcs-deploy-tempfolder"
if [[ "${base-env}" = "prod" ]] || [[ "${base-env}" = "uat" ]]; then
  s3bucket="dcs-deploy-prod-tempfolder"
fi
aws s3 cp s3://$s3bucket/planning/commonlib.tar.gz . 2>>/tmp/install.log
echo "downloaded depends...">> /tmp/install.log

##upzip## to /data/common/lib
tar -xzvf commonlib.tar.gz 2>>/tmp/install.log
rm -f commonlib.tar.gz

##install##
echo "/data/common/lib" >>/etc/ld.so.conf
ldconfig
echo "installed depends...">> /tmp/install.log

##gen crontab##
cd -
TMP_OLD_CRON=oldcrontab
crontab -l > $TMP_OLD_CRON
echo "* * * * * sh /opt/mstar/storage/calculation2.1/work_pln/bin/start.sh autocheck > /dev/null 2>&1" >> $TMP_OLD_CRON
cat $TMP_OLD_CRON | crontab
rm -f $TMP_OLD_CRON
echo "generaled crontab...">> /tmp/install.log

##some setting
#keep min memory > 1G
echo "vm.min_free_kbytes = 1048576" >> /etc/sysctl.conf

mkdir -p /data/app_logs/workerd_v2.1_pln_log
mkdir -p /data/app_logs/workerd_v2.1_pln_data
mkdir -p /opt/mstar/storage/calculation2.1/work_pln
ln -sf /data/app_logs/workerd_v2.1_pln_log /opt/mstar/storage/calculation2.1/work_pln/log
ln -sf /data/app_logs/workerd_v2.1_pln_data /opt/mstar/storage/calculation2.1/work_pln/data

echo "install done.">> /tmp/install.log

