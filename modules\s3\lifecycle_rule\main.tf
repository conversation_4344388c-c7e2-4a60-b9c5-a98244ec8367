variable "prefix" {
  type = string
  default = null
}

variable "enabled" {
  type = bool
  default = true
}

variable "expiration" {
  type = number
  default = null
}

variable "nonconcurrent-version-expiration" {
  type = number
  default = 7
}

locals {
  rule = {
    prefix = var.prefix
    enabled = var.enabled
    expiration = {
      days = var.expiration
      expired_object_delete_marker = var.expiration == null? true: null
    }
    noncurrent_version_expiration = {
      default_nonconcurrent_version_expiration = var.nonconcurrent-version-expiration
    }
  }
}

output rule {
  value = local.rule
}
