#!groovy

pipeline {
    parameters {
        choice(choices: 'non-PROD\nPROD', description: 'push to non-PROD or PROD?', name: 'deploy_env')
        string(name: 'GIT_REPO', defaultValue: 'https://msstash.morningstar.com/scm/do/pms.aws-service.script.git', description: 'the repo to fetch code')
        string(name: 'GIT_BRANCH', defaultValue: 'release/1.2', description: 'the branch to build')
        string(name: 'ECS_TAG_VERSION', defaultValue: '', description: 'Optional additional docker image tag. Set the docker image tag to match task definition')
        choice(name: 'ARTIFACT_GROUP', choices: '''
do
''', description: 'name of the ECR repository is like ARTIFACT_GROUP/ARTIFACT_NAME')
        choice(name: 'NEXUS_ARTIFACT_REPOSITORY', choices: '''
DO-snapshots
DO-releases
''', description: 'repository of the artifact to retrieve from nexus')
        choice(name: 'NEXUS_ARTIFACT_GROUP', choices: '''
com.morningstar
''', description: 'groupId of the artifact to retrieve from nexus')
        choice(name: 'ARTIFACT_NAME', choices: '''
portfolio-data-agent-esg
dcs-images-repo
portfolio-service
portfolio-setting-service
''', description: 'name of the artifact to retrieve from nexus')
        string(name: 'NEXUS_ARTIFACT_VERSION', defaultValue: '', description: 'version of the artifact to retrieve from nexus, also as a docker image tag, like "1.29-SNAPSHOT"')
        booleanParam(name: 'DR', defaultValue: false, description: 'Push to DR environment at the same time')
        booleanParam(name: 'TAG_EXISTING', defaultValue: true, description: 'If image had been built for the given Nexus artifact version then tag only and skip building')
    }
    environment {
        NONPROD_ACCOUNT = '************'
        NONPROD_ROLE = 'DO-ENGR-Deploy'
        PROD_ACCOUNT = '************'
        PROD_ROLE = 'DO-ENGR-Deploy'
        DOCKER_SCRIPTS = "${WORKSPACE}/docker/scripts"
    }

    options {
        ansiColor('xterm')
        skipDefaultCheckout()
        skipStagesAfterUnstable()
    }

    agent { label 'awsjenklinux' }

    stages {

        stage('download source code') {
            steps {
                // cleanWs
                deleteDir()
                echo 'download source code ...'
                git credentialsId: '8d2dd645-3268-4220-bdae-14d5be0ae0eb', url: '${GIT_REPO}', branch: '${GIT_BRANCH}'
                stash includes: '**/*', name: 'build'
            }
        }

        stage('fetch image') {
            when {
                expression {
                    params.TAG_EXISTING && !(params.NEXUS_ARTIFACT_VERSION =~ /SNAPSHOT/)
                }
            }
            steps {
                script {
                    env.TAG_EXISTING = 'yes'
                }
                sh 'export AWS_REGION=us-east-1; sh ${DOCKER_SCRIPTS}/fetch-image.sh'
            }
        }

        stage('build image') {
            steps {
                sh ''' aws --version '''
                deleteDir()
                unstash 'build'
                sh 'export AWS_REGION=us-east-1; sh ${DOCKER_SCRIPTS}/build-image.sh'
            }
        }

        stage('push image') {
            steps {
                sh 'export AWS_REGION=us-east-1; sh ${DOCKER_SCRIPTS}/push-image.sh'
            }
        }

        stage('push image to DR') {
            when {
                expression {
                    params.DR
                }
            }
            steps {
                sh 'export AWS_REGION=us-west-2; sh ${DOCKER_SCRIPTS}/push-image.sh'
            }
        }
    }
}