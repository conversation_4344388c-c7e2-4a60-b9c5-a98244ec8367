module "common-iam-policies-apigateway" {
  source = "../../modules/iam/policies"
  templates = [
    "vpce_except",
  ]
  vars = {
    vpce_id = jsonencode(local.vpce_id)
  }
}

module "common-iam" {
  source = "../../modules/iam/resource"
  mstar_env = var.mstar_env
  name = "data-agent-esg-role"
  use_role_name_prefix = false
  service_prefix = ""
  assume_role_policy = module.common-iam-const.assume-role-policy-lambda
  policies = distinct(concat(module.common-iam-const.iam-policies-lambda-vpc, module.common-iam-const.iam-policies-lambda-basic, module.common-iam-const.iam-policies-lambda-role))
  policy = list(module.common-iam-policies.template_content)
  iam_role_tags = module.esg-tags.default_iam_role_tags
}

module "common-iam-const" {
  source = "../../modules/iam/const"
}

module "common-caller" {
  source = "../../modules/caller"
}

module "common-iam-policies" {
  source = "../../modules/iam/policies"
  templates = [
    "ssm",
    "kms",
    "s3",
    "s3crud",
    "sqsTx"
  ]
  vars = {
    parameter_kms_key_arn = module.common-kms.ref-pas-data-result-key-arn
    parameter_keys = jsonencode(list(module.common-kms.para-ondemand-jobs-rds-password[0],module.common-kms.para-auth-data-key[0]))
    kms_key_arns = jsonencode(list(local.key_arn, module.common-kms.ref-pas-data-result-key-arn))
    input_buckets = jsonencode(list(local.s3_pas_esg_results_arn, local.s3_pas_fi_results_arn, local.s3_do_pas_results_arn))
    input_bucket_files = jsonencode(list("${local.s3_pas_esg_results_arn}/*", "${local.s3_pas_fi_results_arn}/*", "${local.s3_do_pas_results_arn}/*"))
    output_buckets = jsonencode(list(local.s3_pas_holding_arn, local.s3_do_pas_holding_arn,local.s3_do_pas_results_arn))
    output_bucket_files = jsonencode(list("${local.s3_pas_holding_arn}/*", "${local.s3_do_pas_holding_arn}/*","${local.s3_do_pas_results_arn}/*"))
    sqs_transmit_queues = jsonencode(list(
    replace(local.sqs_ondmand_arn_template,"region_xxx:env_xxx" ,"${var.default_region}:${local.account_id}:${local.sqs_env}" ),
    replace(local.sqs_ondmand_batch_arn_template,"region_xxx:env_xxx" ,"${var.default_region}:${local.account_id}:${local.sqs_env}" ),
    replace(local.sqs_ondmand_fixincome_template,"region_xxx:env_xxx" ,"${var.default_region}:${local.account_id}:${local.sqs_env}" ),
    replace(local.sqs_ondmand_fixincome_batch_arn_template,"region_xxx:env_xxx" ,"${var.default_region}:${local.account_id}:${local.sqs_env}" ),
    replace(local.sqs_ondmand_carbon_arn_template,"region_xxx:env_xxx" ,"${var.default_region}:${local.account_id}:${local.sqs_env}" ),
    replace(local.sqs_ondmand_carbon_batch_arn_template,"region_xxx:env_xxx" ,"${var.default_region}:${local.account_id}:${local.sqs_env}" ),
    replace(local.sqs_ondmand_globalRating_arn_template,"region_xxx:env_xxx" ,"${var.default_region}:${local.account_id}:${local.sqs_env}" ),
    replace(local.sqs_ondmand_carbon2_arn_template,"region_xxx:env_xxx" ,"${var.default_region}:${local.account_id}:${local.sqs_env}" ),
    replace(local.sqs_ondmand_carbon2_batch_arn_template,"region_xxx:env_xxx" ,"${var.default_region}:${local.account_id}:${local.sqs_env}" ),
    replace(local.sqs_ondmand_lctr_arn_template,"region_xxx:env_xxx" ,"${var.default_region}:${local.account_id}:${local.sqs_env}" ),
    replace(local.sqs_ondmand_lctr_batch_arn_template,"region_xxx:env_xxx" ,"${var.default_region}:${local.account_id}:${local.sqs_env}" ),
    replace(local.sqs_ondmand_eutx_arn_template,"region_xxx:env_xxx" ,"${var.default_region}:${local.account_id}:${local.sqs_env}" ),
    replace(local.sqs_ondmand_eutx_batch_arn_template,"region_xxx:env_xxx" ,"${var.default_region}:${local.account_id}:${local.sqs_env}" ),
    replace(local.sqs_ondmand_globalRating_batch_arn_template,"region_xxx:env_xxx" ,"${var.default_region}:${local.account_id}:${local.sqs_env}" ),
    replace(local.sqs_ondmand_esgImpact_arn_template,"region_xxx:env_xxx" ,"${var.default_region}:${local.account_id}:${local.sqs_env}" ),
    replace(local.sqs_ondmand_esgImpact_batch_arn_template,"region_xxx:env_xxx" ,"${var.default_region}:${local.account_id}:${local.sqs_env}" ),
    replace(local.sqs_ondmand_esgPai_arn_template,"region_xxx:env_xxx" ,"${var.default_region}:${local.account_id}:${local.sqs_env}" ),
    replace(local.sqs_ondmand_esgPai_batch_arn_template,"region_xxx:env_xxx" ,"${var.default_region}:${local.account_id}:${local.sqs_env}" ),
    replace(local.sqs_ondmand_gender_arn_template,"region_xxx:env_xxx" ,"${var.default_region}:${local.account_id}:${local.sqs_env}" ),
    replace(local.sqs_ondmand_gender_batch_arn_template,"region_xxx:env_xxx" ,"${var.default_region}:${local.account_id}:${local.sqs_env}" ),
    replace(local.sqs_ondmand_arn_template,"region_xxx:env_xxx" ,"${var.dr_region}:${local.account_id}:${local.sqs_env}dr-" ),
    replace(local.sqs_ondmand_batch_arn_template,"region_xxx:env_xxx" ,"${var.dr_region}:${local.account_id}:${local.sqs_env}dr-" ),
    replace(local.sqs_ondmand_fixincome_template,"region_xxx:env_xxx" ,"${var.dr_region}:${local.account_id}:${local.sqs_env}dr-" ),
    replace(local.sqs_ondmand_fixincome_batch_arn_template,"region_xxx:env_xxx" ,"${var.dr_region}:${local.account_id}:${local.sqs_env}dr-" ),
    replace(local.sqs_ondmand_carbon_arn_template,"region_xxx:env_xxx" ,"${var.dr_region}:${local.account_id}:${local.sqs_env}dr-" ),
    replace(local.sqs_ondmand_carbon_batch_arn_template,"region_xxx:env_xxx" ,"${var.dr_region}:${local.account_id}:${local.sqs_env}dr-" ),
    replace(local.sqs_ondmand_carbon2_arn_template,"region_xxx:env_xxx" ,"${var.dr_region}:${local.account_id}:${local.sqs_env}dr-" ),
    replace(local.sqs_ondmand_carbon2_batch_arn_template,"region_xxx:env_xxx" ,"${var.dr_region}:${local.account_id}:${local.sqs_env}dr-" ),
    replace(local.sqs_ondmand_lctr_arn_template,"region_xxx:env_xxx" ,"${var.dr_region}:${local.account_id}:${local.sqs_env}dr-" ),
    replace(local.sqs_ondmand_lctr_batch_arn_template,"region_xxx:env_xxx" ,"${var.dr_region}:${local.account_id}:${local.sqs_env}dr-" ),
    replace(local.sqs_ondmand_eutx_arn_template,"region_xxx:env_xxx" ,"${var.dr_region}:${local.account_id}:${local.sqs_env}dr-" ),
    replace(local.sqs_ondmand_eutx_batch_arn_template,"region_xxx:env_xxx" ,"${var.dr_region}:${local.account_id}:${local.sqs_env}dr-" ),
    replace(local.sqs_ondmand_globalRating_arn_template,"region_xxx:env_xxx" ,"${var.dr_region}:${local.account_id}:${local.sqs_env}dr-" ),
    replace(local.sqs_ondmand_globalRating_batch_arn_template,"region_xxx:env_xxx" ,"${var.dr_region}:${local.account_id}:${local.sqs_env}dr-" ),
    replace(local.sqs_ondmand_esgImpact_arn_template,"region_xxx:env_xxx" ,"${var.dr_region}:${local.account_id}:${local.sqs_env}" ),
    replace(local.sqs_ondmand_esgImpact_batch_arn_template,"region_xxx:env_xxx" ,"${var.dr_region}:${local.account_id}:${local.sqs_env}" ),
    replace(local.sqs_ondmand_esgPai_arn_template,"region_xxx:env_xxx" ,"${var.dr_region}:${local.account_id}:${local.sqs_env}" ),
    replace(local.sqs_ondmand_esgPai_batch_arn_template,"region_xxx:env_xxx" ,"${var.dr_region}:${local.account_id}:${local.sqs_env}" ),
    replace(local.sqs_ondmand_gender_arn_template,"region_xxx:env_xxx" ,"${var.dr_region}:${local.account_id}:${local.sqs_env}" ),
    replace(local.sqs_ondmand_gender_batch_arn_template,"region_xxx:env_xxx" ,"${var.dr_region}:${local.account_id}:${local.sqs_env}" )
    ))
  }
}

locals {
  vpce_id = module.common-prefix.isPROD || module.common-prefix.isUAT? local.prod_vpce_id:local.nonprod_vpce_id
  prod_vpce_id = module.common-prefix.isDR? var.para_prod_dr_vpce_id:var.para_prod_vpce_id
  nonprod_vpce_id = module.common-prefix.isDR? var.para_nonprod_dr_vpce_id:var.para_nonprod_vpce_id
  kms_region = module.common-prefix.isPROD || module.common-prefix.isUAT? local.prod_kms_region:local.nonprod_kms_region
  prod_kms_region = module.common-prefix.isDR? "${var.dr_region}:${var.datasvc_prod}":"${var.default_region}:${var.datasvc_prod}"
  nonprod_kms_region = module.common-prefix.isDR? "${var.dr_region}:${var.datasvc_nonprod}":"${var.default_region}:${var.datasvc_nonprod}"
  key_arn = "arn:aws:kms:${local.kms_region}:key/${local.kms_id}"
  kms_id = module.common-prefix.isPROD || module.common-prefix.isUAT? local.prod_kms_id:local.nonprod_kms_id
  prod_kms_id =  module.common-prefix.isDR? var.para_prod_dr_kms_id:var.para_prod_kms_id
  nonprod_kms_id = var.para_nonprod_kms_id
  s3_pas_holding_arn = "arn:aws:s3:::${local.bucket_prefix-}mstar-pas-holdings"
  s3_pas_esg_results_arn = "arn:aws:s3:::${local.bucket_prefix-}mstar-pas-esg-results"
  s3_pas_fi_results_arn = "arn:aws:s3:::${local.bucket_prefix-}mstar-pas-fixedincome-results"
  s3_do_pas_results_arn = "arn:aws:s3:::${local.bucket_prefix-}do-mstar-pas-results"
  s3_do_pas_holding_arn = "arn:aws:s3:::${local.bucket_prefix-}do-mstar-pas-holdings"

  sqs_env = module.common-prefix.isPROD || module.common-prefix.isUAT ? "":module.common-prefix.base-env-
  account_id = module.common-prefix.isPROD || module.common-prefix.isUAT ? var.datasvc_prod:var.datasvc_nonprod

  sqs_ondmand_arn_template = "arn:aws:sqs:region_xxx:env_xxxondemand-jobs-queue"
  sqs_ondmand_batch_arn_template = "arn:aws:sqs:region_xxx:env_xxxondemand-batch-jobs-queue"
  sqs_ondmand_fixincome_template = "arn:aws:sqs:region_xxx:env_xxxondemand-jobs-queue-fixedincome"
  sqs_ondmand_fixincome_batch_arn_template = "arn:aws:sqs:region_xxx:env_xxxondemand-batch-jobs-queue-fixedincome"
  sqs_ondmand_carbon_arn_template = "arn:aws:sqs:region_xxx:env_xxxondemand-jobs-queue-carbon"
  sqs_ondmand_carbon_batch_arn_template = "arn:aws:sqs:region_xxx:env_xxxondemand-batch-jobs-queue-carbon"
  sqs_ondmand_globalRating_arn_template = "arn:aws:sqs:region_xxx:env_xxxondemand-jobs-queue-esg-globes"
  sqs_ondmand_globalRating_batch_arn_template = "arn:aws:sqs:region_xxx:env_xxxondemand-batch-jobs-queue-esg-globes"
  sqs_ondmand_esgImpact_arn_template = "arn:aws:sqs:region_xxx:env_xxxdp-impact-metrics-pas-ondemand"
  sqs_ondmand_esgImpact_batch_arn_template = "arn:aws:sqs:region_xxx:env_xxxdp-impact-metrics-pas-batch"
  sqs_ondmand_esgPai_arn_template = "arn:aws:sqs:region_xxx:env_xxxdp-pai-pas-ondemand"
  sqs_ondmand_esgPai_batch_arn_template = "arn:aws:sqs:region_xxx:env_xxxdp-pai-pas-batch"
  sqs_ondmand_gender_arn_template = "arn:aws:sqs:region_xxx:env_xxxondemand-jobs-queue-gender-diversity"
  sqs_ondmand_gender_batch_arn_template = "arn:aws:sqs:region_xxx:env_xxxondemand-batch-jobs-queue-gender-diversity"
  sqs_ondmand_carbon2_arn_template = "arn:aws:sqs:region_xxx:env_xxxdp-carbon-2_0-pas-ondemand"
  sqs_ondmand_carbon2_batch_arn_template = "arn:aws:sqs:region_xxx:env_xxxdp-carbon-2_0-pas-batch"
  sqs_ondmand_lctr_arn_template = "arn:aws:sqs:region_xxx:env_xxxdp-lctr-pas-ondemand"
  sqs_ondmand_lctr_batch_arn_template = "arn:aws:sqs:region_xxx:env_xxxdp-lctr-pas-batch"
  sqs_ondmand_eutx_arn_template = "arn:aws:sqs:region_xxx:env_xxxdp-eutx-pas-ondemand"
  sqs_ondmand_eutx_batch_arn_template = "arn:aws:sqs:region_xxx:env_xxxdp-eutx-pas-batch"
}

variable para_prod_dr_vpce_id{
  default = "vpce-0a9f28df3323cfdb2"
}

variable para_prod_vpce_id {
  default = "vpce-07e683f6e5f01456d"
}

variable para_nonprod_dr_vpce_id {
  default = "vpce-039404114856da0aa"
}

variable para_nonprod_vpce_id {
  default = "vpce-0507da66c3702aed2"
}

variable default_region {
  default = "us-east-1"
}

variable dr_region {
  default = "us-west-2"
}

variable datasvc_prod {
  default = "062740692535"
}

variable datasvc_nonprod {
  default = "187914334366"
}

variable para_prod_dr_kms_id {
  default = "a0e2bee4-d10f-4a8e-b9f6-b00c276fc815"
}

variable para_prod_kms_id {
  default = "7361aa94-87fe-4a1a-a817-edbf2ccad470"
}

variable para_nonprod_kms_id {
  default = "1df59320-2d92-4b59-90bf-953020d4d504"
}

locals {
  bucket_prefix- = local.isQA? "stg-": local._bucket_prefix-
  _bucket_prefix- = module.common-prefix.isUAT? "":module.common-prefix.env-
  isQA = module.common-prefix.ENV == "QA"
}
