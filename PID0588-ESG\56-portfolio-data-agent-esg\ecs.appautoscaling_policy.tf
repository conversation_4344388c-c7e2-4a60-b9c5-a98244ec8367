data "aws_iam_role" "ecsAutoScaling" {
  name = "AWSServiceRoleForApplicationAutoScaling_ECSService"
}

resource aws_appautoscaling_target esg-data-agent-appautoscaling-target {
  max_capacity = var.max_capacity
  min_capacity = var.min_capacity
  resource_id = "service/${module.common-prefix.env-}${local.cluster_name}/${module.common-prefix.env-}${local.name}"
  role_arn = data.aws_iam_role.ecsAutoScaling.arn
  scalable_dimension = "ecs:service:DesiredCount"
  service_namespace = "ecs"
}

variable "min_capacity" {
  default = 2
}

variable "max_capacity" {
  default = 4
}

resource aws_appautoscaling_policy esg-data-agent-scale-out-cpu {
  name = "${module.common-prefix.env-}esg-data-agent-scale-out-cpu"
  resource_id = aws_appautoscaling_target.esg-data-agent-appautoscaling-target.resource_id
  scalable_dimension = "ecs:service:DesiredCount"
  service_namespace = "ecs"
  policy_type = "StepScaling"
  step_scaling_policy_configuration {
    adjustment_type = "ChangeInCapacity"
    cooldown = 60
    metric_aggregation_type = "Average"
    step_adjustment {
      metric_interval_lower_bound = 0
      scaling_adjustment = 1
    }
  }
  provisioner "local-exec" {
    command = "sleep 15"
  }
}

resource aws_appautoscaling_policy esg-data-agent-scale-in-decreace-cpu-one {
  name = "${module.common-prefix.env-}esg-data-agent-scale-in-decrease-cpu-one"
  resource_id = aws_appautoscaling_target.esg-data-agent-appautoscaling-target.resource_id
  scalable_dimension = "ecs:service:DesiredCount"
  service_namespace = "ecs"
  policy_type = "StepScaling"
  step_scaling_policy_configuration {
    adjustment_type = "ChangeInCapacity"
    cooldown = 60
    metric_aggregation_type = "Average"
    step_adjustment {
      metric_interval_lower_bound = 0
      scaling_adjustment = -1
    }
  }
  provisioner "local-exec" {
    command = "sleep 15"
  }
}