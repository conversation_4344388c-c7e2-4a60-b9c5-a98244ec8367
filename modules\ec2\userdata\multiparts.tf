data template_cloudinit_config config {
  gzip = var.gzip
  base64_encode = var.gzip || var.base64_encode

  part {
    content_type = "text/cloud-boothook"
    content = var.boothook
  }

  part {
    content_type = "text/x-shellscript"
    content = var.user_script
  }
}

output multiparts {
  value = data.template_cloudinit_config.config.rendered
}

variable gzip {
  default = true
}

variable base64_encode {
  default = true
}
