module "s3-mstar-pas-results" {
  source = "../../modules/s3/resource/replication/encrypted"
  providers = {
    aws.replication = aws.replication
  }
  name = "do-mstar-pas-results"
  mstar_env = var.mstar_env
  name_prefix = ""
  s3_bucket_tags = module.esg-tags.default_s3_bucket_tags
  key_name = "alias/portfolio-service/pas-data-result-key"
  replication_key_name = "alias/portfolio-service/pas-data-result-key"
  lifecycle_rules = list(module.s3-lifecycle-pas-holdings-and-results.rule)
}

module "s3-mstar-pas-holdings" {
  source = "../../modules/s3/resource/replication/encrypted"
  providers = {
    aws.replication = aws.replication
  }
  name = "do-mstar-pas-holdings"
  mstar_env = var.mstar_env
  name_prefix = ""
  s3_bucket_tags = module.esg-tags.default_s3_bucket_tags
  key_name = "alias/portfolio-service/pas-data-result-key"
  replication_key_name = "alias/portfolio-service/pas-data-result-key"
  lifecycle_rules = list(module.s3-lifecycle-pas-holdings-and-results.rule)
}