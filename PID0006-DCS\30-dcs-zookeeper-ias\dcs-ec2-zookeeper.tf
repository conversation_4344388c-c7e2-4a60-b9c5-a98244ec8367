module "dcs-ec2-zookeeper" {
  source = "../../modules/ec2only/resource"
  security_groups = module.common-vpc-sg.default_ec2_security_group
  mstar_env = var.mstar_env
  ec2_tags = module.dcs-tags.default_e2only_tags
  name = local.name
  subnet_id = data.aws_subnet.selected.id
  availability_zone = var.ec2_aws_availability_zone
  aws_instance_type = var.aws_instance_type
  ec2_count = var.ec2_instance_count
  name-prefix = module.common-prefix.env_prefix
  user_data = local.user_data
  farm-name = var.mstar_env == "PROD" ? "3x":""
  image_id = var.image_id
  aws_instance_profile = "IAS-aws-s3-fullaccess"
  key_name = var.key_name
}



locals {
  name = "dcs_zookeeper"
  user_data =  join("\n", tolist([
  module.common-ec2-userdata-const.install_dcs_zookeeper_ias]))
}

variable aws_instance_type {
  default = "m5.large"
}

variable ec2_instance_count {
  default = 1
}

variable ec2_mobile_instance_count {
  default = 0
}

variable ec2_precalc_instance_count {
  default = 0
}

variable ec2_dwm_instance_count {
  default = 0
}

variable additional_volumes {
  default = [
    {
      device_name = "/dev/xvda"
      delete_on_termination = true
      encrypted = false
      volume_size = 60
      volume_type = "gp2"
    }
  ]
}

variable image_id {
  default = "ami-0688abfbf28490c22"
}

module "common-ec2-userdata-const" {
  source = "../../modules/ec2/userdata/const"
  mstar_env = var.mstar_env
}

variable "aws_zone_name_dataapi" {}

module "dcs_zookeeper_route53" {
  source = "../../modules/route53"
  name_ips = module.dcs-ec2-zookeeper.private_name_ips
  aws_zone_name = var.aws_zone_name_dataapi
}
