module "common-prefix" {
  source = "../../modules/prefix"
  mstar_env = var.mstar_env
}

module "common-vpc-sg" {
  source = "../../modules/vpc/sg"
  aws_security_groups = var.aws_security_groups
  default_lb_security_group = var.default_lb_security_group
}

module "common-vpc" {
  source = "../../modules/vpc"
  aws_vpc = var.aws_vpc
  aws_availability_zone = var.aws_availability_zone
}

data aws_s3_bucket_object lambda {
  bucket = local.s3_lambda_bucket
  key = "${module.common-prefix.env}/${var.ecs_tag_version}/portfolio-data-agent-esg-lambda.jar"
}

locals {
  region = !module.common-prefix.isDR ? "us-east-1" : "us-west-2"
  s3_lambda_bucket = module.common-prefix.isPROD || module.common-prefix.isUAT? local._prod_s3_lambda_bucket : local._non_prod_s3_lambda_bucket
  _non_prod_s3_lambda_bucket = module.common-prefix.isNonProdDR?var.dr_nonprod_s3_lambda_bucket : var.nonprod_s3_lambda_bucket
  _prod_s3_lambda_bucket = module.common-prefix.isDR? var.dr_s3_lambda_bucket : var.prod_s3_lambda_bucket

  s3_result_bucket = "${local.bucket_prefix-}do-mstar-pas-results"
  s3_do_pas_results_arn = "arn:aws:s3:::${local.bucket_prefix-}do-mstar-pas-results"
  bucket_prefix- = local.isQA? "stg-": local._bucket_prefix-
  _bucket_prefix- = module.common-prefix.isUAT? "":module.common-prefix.env-
  isQA = module.common-prefix.ENV == "QA"
}

variable prod_s3_lambda_bucket {
  default = "prod-codedeploy-data-agent"
}

variable dr_s3_lambda_bucket {
  default = "dr-codedeploy-data-agent"
}

variable nonprod_s3_lambda_bucket {
  default = "nonprod-codedeploy-data-agent"
}

variable dr_nonprod_s3_lambda_bucket {
  default = "dr-nonprod-codedeploy-data-agent"
}

variable "ecs_tag_version" {}

variable aws_vpc {}

variable mstar_env {}

variable aws_security_groups {}

variable default_lb_security_group {}

variable aws_availability_zone {}