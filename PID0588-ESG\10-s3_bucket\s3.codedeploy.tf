resource aws_s3_bucket_public_access_block no_replication {
  count = local.enable_bucket?1:0
  bucket = aws_s3_bucket.no_replication[0].id
  block_public_acls = true
  block_public_policy = true
  ignore_public_acls = true
  restrict_public_buckets = true
  depends_on = [
    aws_s3_bucket.no_replication
  ]
}

resource aws_s3_bucket no_replication {
  count = local.enable_bucket?1:0
  bucket = local._codeploy_bucket_name
  tags = module.esg-tags.default_s3_bucket_tags
  versioning {
    enabled = true
  }
  dynamic lifecycle_rule {
    for_each = local.lifecycle_rules
    iterator = _
    content {
      enabled = _.value.enabled
      prefix = _.value.prefix
      expiration {
        days = _.value.expiration.days
        expired_object_delete_marker = _.value.expiration.expired_object_delete_marker
      }
      noncurrent_version_expiration {
        days = _.value.noncurrent_version_expiration.default_nonconcurrent_version_expiration
      }
    }
  }
}

locals {
  enable_bucket = module.common-prefix.ENV == "STG" || module.common-prefix.isPROD || module.common-prefix.isNonProdDR
  _codeploy_bucket_name = module.common-prefix.isPROD || module.common-prefix.isUAT? local._prod_codedeploy_bucket : local._non_prod_s3_lambda_bucket
  _prod_codedeploy_bucket = module.common-prefix.isDR? var.dr_codedeploy_bucket : var.prod_codedeploy_bucket
  _non_prod_s3_lambda_bucket = module.common-prefix.isNonProdDR?var.nonprod_codedeploy_bucket : var.dr_nonprod_codedeploy_bucket
  lifecycle_rules = distinct(concat(list(module.common-s3-lifecycle_rule.rule), var.lifecycle_rules))
}

variable lifecycle_rules {
  type = list(object({
    prefix = string
    enabled = bool
    expiration = object({
      days = number
      expired_object_delete_marker = bool
    })
    noncurrent_version_expiration = object({
      default_nonconcurrent_version_expiration = number
    })
  }))
  default = []
}

variable prod_codedeploy_bucket {
  default = "prod-codedeploy-data-agent"
}

variable dr_codedeploy_bucket {
  default = "dr-codedeploy-data-agent"
}

variable nonprod_codedeploy_bucket {
  default = "nonprod-codedeploy-data-agent"
}

variable dr_nonprod_codedeploy_bucket {
  default = "dr_nonprod-codedeploy-data-agent"
}