module "dcs-role-iam" {
  source = "../../modules/iam/resource"
  mstar_env = var.mstar_env
  name = local.dcs_role_name
  use_role_name_prefix = false
  use_env_prefix = false
  service_prefix = ""
  assume_role_policy = module.common-iam-const.assume-role-policy-ec2
  policies = distinct(concat(module.common-iam-const.iam-cloudagent,module.common-iam-const.iam-policies-ec2))
  policy = list(module.dcs-iam-policies.template_content)
  iam_role_tags = module.dcs-tags.default_iam_role_tags2
  path = "/dcs/"
}

module "common-iam-const" {
  source = "../../modules/iam/const"
}

module "common-caller" {
  source = "../../modules/caller"
}

module "dcs-iam-policies" {
  source = "../../modules/iam/policies"
  templates = [
    "s3crud",
  ]
  vars = {
    output_buckets = jsonencode(list(local.s3_dcs_deploy_folder_arn))
    output_bucket_files = jsonencode(list("${local.s3_dcs_deploy_folder_arn}/*"))
  }
}

locals {
  dcs_role_name = "dcs_role"
  s3_deploy_foler_name = "dcs-deploy-folder"
  s3_dcs_deploy_folder_arn = upper(var.mstar_env) == "PROD"||upper(var.mstar_env) == "UAT"? "arn:aws:s3:::dcs-deploy-prod-tempfolder":"arn:aws:s3:::dcs-deploy-tempfolder"
}

variable "mstar_env" {
}

resource "aws_iam_instance_profile" "dcs_role" {
  name = "dcs_role"
  role = "dcs_role"
}
