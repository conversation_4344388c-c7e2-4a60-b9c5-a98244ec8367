# Analytics Products Group

variable apg_nonprod {
  default = 957772357715
}

variable apg_prod {
  default = 566439865916
}

output apg_prod {
  value = "arn:aws:iam::${var.apg_prod}:root"
}

output apg_nonprod {
  value = "arn:aws:iam::${var.apg_nonprod}:root"
}

output apg_qa_DataCatalyst_EMR {
  value = "arn:aws:iam::${var.apg_nonprod}:role/qa-DataCatalyst-EmrEc2-Role"
}

output apg_stg_DataCatalyst_EMR {
  value = "arn:aws:iam::${var.apg_nonprod}:role/stg-DataCatalyst-EmrEc2-Role"
}

output apg_prod_DataCatalyst_EMR {
  value = "arn:aws:iam::${var.apg_prod}:role/prod-DataCatalyst-EmrEc2-Role"
}

output apg_prod_operator {
  value = "arn:aws:iam::${var.apg_prod}:role/mstar-apg-prod-operator"
}

output apg_prod_readonly {
  value = "arn:aws:iam::${var.apg_prod}:role/mstar-apg-prod-readonly"
}

output apg_nonprod_operator {
  value = "arn:aws:iam::${var.apg_nonprod}:role/mstar-apg-non-prod-operator"
}

output apg_nonprod_ECS {
  value = "arn:aws:iam::${var.apg_nonprod}:role/apg-service-ecs-task-role"
}

output apg_prod_ECS {
  value = "arn:aws:iam::${var.apg_prod}:role/apg-service-ecs-task-role"
}

output apg_nonprod_EMR {
  value = "arn:aws:iam::${var.apg_nonprod}:role/apg-emr-resource-role"
}

output apg_prod_EMR {
  value = "arn:aws:iam::${var.apg_prod}:role/apg-emr-resource-role"
}
