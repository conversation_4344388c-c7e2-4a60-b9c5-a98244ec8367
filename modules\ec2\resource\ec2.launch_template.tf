resource aws_launch_template lt {
  name_prefix = module.common-prefix.env-prefix
  iam_instance_profile {
    name = data.aws_iam_instance_profile.rp.name
  }
  image_id = local.image_id
  instance_type = var.aws_instance_type
  credit_specification {
    cpu_credits = var.cpu_credits
  }
  ebs_optimized = replace(var.aws_instance_type, "/^t2/", "") == var.aws_instance_type
  user_data = local.user_data_base64
  vpc_security_group_ids = var.security_groups
  key_name = var.key_name
  monitoring {
    enabled = false
  }
  tags = var.launch_template_tags
  tag_specifications {
    resource_type = "instance"
    tags = merge(var.launch_template_tag_specification_tags, {
      Name = module.common-prefix.env-prefix
    })
  }
  lifecycle {
    create_before_destroy = true
  }
  dynamic block_device_mappings {
    for_each = var.block_device_mapping
    iterator = _
    content {
      device_name = _.value.device_name
      dynamic ebs {
        for_each = _.value.ebs
        content {
          delete_on_termination = ebs.value.delete_on_termination
        }
      }
      no_device = _.value.no_device
      virtual_name = _.value.virtual_name
    }
  }
}

variable block_device_mapping {
  type = list(object({
    device_name = string
    ebs = list(object( {
      delete_on_termination = bool
    }))
    no_device = bool
    virtual_name = string
  }))
  default = []
}

variable launch_template_tags {
  type = map(any)
}

variable launch_template_tag_specification_tags {
  type = map(any)
  default = {}
}

variable cpu_credits {
  default = "unlimited"
}

module common-ami-ecs_image {
  source = "../../ami/linux2_ecs"
}

data aws_iam_instance_profile "rp" {
  name = var.aws_instance_profile
}

variable aws_instance_profile {
  default = "ecsInstanceRoleDO"
}

variable aws_instance_type {
  default = "t3.medium"
}

variable image_id {
  default = ""
}

variable user_data {
  default = ""
}

variable user_data_base64 {
  default = ""
}

variable "key_name" {
  default = ""
}

locals {
  image_id = length(var.image_id) > 0? var.image_id: module.common-ami-ecs_image.id
  user_data_base64 = length(var.user_data_base64) > 0? var.user_data_base64: base64encode(var.user_data)
}
