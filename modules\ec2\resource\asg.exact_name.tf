resource aws_autoscaling_group asg_with_exact_name_with_launch_template {
  count = !var.use_asg_name_prefix && var.enable_scaling && var.enable_asg? 1:0
  name = module.common-prefix.env-prefix
  launch_template {
    id = aws_launch_template.lt.id
    version = aws_launch_template.lt.latest_version
  }
  max_size = var.max_size
  min_size = var.min_size
  desired_capacity = var.initial_capacity
  availability_zones = var.availability_zones
  vpc_zone_identifier = var.subnet_ids
  tags = var.asg_tags
  force_delete = true
  lifecycle {
    create_before_destroy = true
    ignore_changes = [
      desired_capacity,
    ]
  }
}

resource aws_autoscaling_group asg_with_exact_name_with_launch_template-no-scaling {
  count = !var.use_asg_name_prefix && !var.enable_scaling && var.enable_asg? 1:0
  name = module.common-prefix.env-prefix
  launch_template {
    id = aws_launch_template.lt.id
    version = aws_launch_template.lt.latest_version
  }
  max_size = var.max_size
  min_size = var.min_size
  desired_capacity = var.initial_capacity
  availability_zones = var.availability_zones
  vpc_zone_identifier = var.subnet_ids
  tags = var.asg_tags
  force_delete = true
  lifecycle {
    create_before_destroy = true
  }
}
