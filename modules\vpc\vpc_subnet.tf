data aws_subnet public {
  vpc_id = data.aws_vpc.default.id
  count = length(var.aws_availability_zone)
  filter {
    name = "tag:Name"
    values = [
      "${var.aws_availability_zone[count.index]}-public",
    ]
  }
}

output default_public_subnet_ids_by_enumeration {
  value = data.aws_subnet.public[*].id
}

data aws_subnet private {
  vpc_id = data.aws_vpc.default.id
  count = length(var.aws_availability_zone)
  filter {
    name = "tag:Name"
    values = [
      "${var.aws_availability_zone[count.index]}-private",
    ]
  }
}

output default_private_subnet_ids_by_enumeration {
  value = data.aws_subnet.private[*].id
}

variable aws_availability_zone {
  type = list(string)
  default = [
  ]
}

output default_ec2_availability_zones {
  value = sort(var.aws_availability_zone)
}
