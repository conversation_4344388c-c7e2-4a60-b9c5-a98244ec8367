mstar_env = "PROD"
aws_region = "us-east-1"
aws_vpc_selected = "vpc-74f67c12"

ec2_aws_availability_zone = "us-east-1d"
special_name = "us-east-1d-private"

aws_security_groups = [
  "console",
  "private_app",
  "private_active_directory_client",
  "private_dcs_prod"
]

default_outposts_security_group = [
  "console",
  "private_app",
  "private_active_directory_client",
  "private_dcs_prod"
]

ec2_instance_count = 1

# Amazon Linux 2023 AMI
image_id = "ami-05ffe3c48a9991133"
key_name = "do3x-prod"

aws_instance_type = "r6i.xlarge"

aws_zone_name_dataapi = "do41368.eas.morningstar.com"
