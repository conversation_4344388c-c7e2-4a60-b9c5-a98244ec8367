resource "aws_lb_target_group" "lb-group-pcs" {
  name = trimspace(substr(format("%s%s                ", module.common-prefix.env-, "lb-group-data-agent"), 0, 16))
  port = 80
  protocol = "HTTP"
  target_type = "ip"
  vpc_id = module.common-vpc.default_vpc

  health_check {
    interval = 300
    path = "/${module.common-prefix.env}/healthCheck"
    port = 80
    protocol = "HTTP"
    timeout = 5
    healthy_threshold = 5
    unhealthy_threshold = 2
    matcher = "200"
  }
  tags = module.esg-tags.default_lb_target_group_tags
}
