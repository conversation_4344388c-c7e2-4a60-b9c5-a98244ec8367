#!/usr/bin/env bash

#echo "\#(o_o)#/"

sudo su

echo "start to build dcs-zookeeper env:${base-env}">> /tmp/install.log

echo "update time zone...">> /tmp/install.log
export TZ=America/Chicago
ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

s3bucket="dcs-deploy-tempfolder-ias"
if [[ "${base-env}" = "prod" ]] || [[ "${base-env}" = "uat" ]]; then
  s3bucket="dcs-deploy-prod-tempfolder-ias"
fi

aws s3 cp s3://$s3bucket/zookeeper/zookeeper-3.4.5.tar.gz . 2>>/tmp/install.log
echo "downloaded depends (zookeeper-3.4.5.tar.gz)...">> /tmp/install.log

tar -xzvf zookeeper-3.4.5.tar.gz -C /data/ 2>>/tmp/install.log

rm -f zookeeper-3.4.5.tar.gz

echo "install Java ..."
yum install -y java-17-amazon-corretto-devel

echo "install done.">> /tmp/install.log

