mstar_env = "QA"
mstar_region = "aws-us-east-1"
aws_vpc = "vpc-49ef652f"
aws_security_groups = [
  "console",
  "private_app",
  "private_db",
  "public",
  "private_active_directory_client",
  "do_web_server",
  "private_app_non-prod_outposts",
  "private_os_services_non-prod_outposts",
  "private_do_web_non-prod_outposts",
  "private_db_non-prod_outposts",
  "private_internet_outpost",
]
aws_availability_zone = [
  "us-east-1a",
  "us-east-1c",
  "us-east-1d",
]
default_elasticache_security_group = [
  "console",
  "private_app",
]
default_ec2_security_group = [
  "console",
  "private_app",
]
default_lb_security_group = [
  "private_app",
]
public_lb_security_group = [
  "public",
]
default_outposts_security_group = [
  "do_web_server",
  "private_app_non-prod_outposts",
  "private_os_services_non-prod_outposts",
  "private_do_web_non-prod_outposts",
  "private_db_non-prod_outposts",
  "private_internet_outpost",
]
aws_zone_name_dataapi = "dod2af5.easn.morningstar.com."
ondemand-job-db-server=[
  "do-nonprod-db.c0opw0b7vlgb.us-east-1.rds.amazonaws.com"
]