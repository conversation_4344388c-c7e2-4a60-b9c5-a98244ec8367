#!/usr/bin/env bash

#echo "\#(o_o)#/"

sudo su

echo "start to build dcs-schedule env:${base-env}">> /tmp/install.log

echo "update time zone...">> /tmp/install.log
export TZ=America/Chicago
ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

s3bucket="dcs-deploy-tempfolder-ias"
if [[ "${base-env}" = "prod" ]] || [[ "${base-env}" = "uat" ]]; then
  s3bucket="dcs-deploy-prod-tempfolder-ias"
fi
aws s3 cp s3://$s3bucket/schedule/common.tar.gz . 2>>/tmp/install.log
echo "downloaded depends...">> /tmp/install.log

##upzip## to /data/common/lib
tar -xzvf common.tar.gz -C /data/ 2>>/tmp/install.log
rm -f common.tar.gz

echo "/data/common/lib" >>/etc/ld.so.conf
ldconfig
echo "installed depends...">> /tmp/install.log

##gen crontab##
cd -
TMP_OLD_CRON=oldcrontab
crontab -l > $TMP_OLD_CRON
echo "* * * * * sh /opt/mstar/storage/calculation2.1/schedule/bin/start.sh autocheck > /dev/null 2>&1" >> $TMP_OLD_CRON
echo "*/10 * * * * sh /opt/mstar/storage/calculation2.1/schedule/graphitecollector/bin/start.sh autocheck > /dev/null 2>&1" >> $TMP_OLD_CRON
echo "30 23 * * * find /var/log/audit -mtime +7 -type f -name 'audit*' -exec rm -f {} \; > /dev/null 2>&1" >> $TMP_OLD_CRON
echo "50 23 * * * find /data/coredump/ -mtime +7 -name "core.*" -exec rm -rf {} \; > /dev/null 2>&1" >> $TMP_OLD_CRON
cat $TMP_OLD_CRON | crontab
rm -f $TMP_OLD_CRON
echo "generaled crontab...">> /tmp/install.log

##some setting
#keep min memory > 1G
echo "vm.min_free_kbytes = 1048576" >> /etc/sysctl.conf

mkdir -p /data/app_logs/schedulerd_v2.1_log
mkdir -p /data/app_logs/schedulerd_v2.1_data
mkdir -p /opt/mstar/storage/calculation2.1/schedule
ln -sf /data/app_logs/schedulerd_v2.1_log /opt/mstar/storage/calculation2.1/schedule/log
ln -sf /data/app_logs/schedulerd_v2.1_data /opt/mstar/storage/calculation2.1/schedule/data


echo "install done.">> /tmp/install.log

