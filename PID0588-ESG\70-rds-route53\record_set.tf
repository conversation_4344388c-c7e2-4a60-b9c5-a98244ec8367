data "aws_route53_zone" "dataapi-public" {
  name = var.aws_zone_name_dataapi
  private_zone = false
}

variable "aws_zone_name_dataapi" {}

resource "aws_route53_record" "cname-portfoliocalc" {
  zone_id = data.aws_route53_zone.dataapi-public.id
  name = "ondemand-job-db${module.common-prefix._-env}"
  type = "CNAME"
  ttl = "300"
  records = var.ondemand-job-db-server
}

variable "ondemand-job-db-server" {
  type = list(string)
  default = []
}