resource aws_launch_template lt {
  name_prefix = module.common-prefix.env-prefix
  iam_instance_profile {
    name = data.aws_iam_instance_profile.rp.name
  }
  image_id = local.image_id
  instance_type = var.aws_instance_type
  credit_specification {
    cpu_credits = var.cpu_credits
  }
  ebs_optimized = replace(var.aws_instance_type, "/^t2/", "") == var.aws_instance_type
  user_data = local.user_data_base64

  key_name = var.key_name
  monitoring {
    enabled = false
  }
  tags = var.launch_template_tags
  tag_specifications {
    resource_type = "instance"
    tags = merge(var.launch_template_tag_specification_tags, {
      Name = module.common-prefix.env-prefix
    })
  }
  lifecycle {
    create_before_destroy = true
  }
  network_interfaces {
    subnet_id = var.subnet_id
    security_groups = var.security_groups
  }
  dynamic "placement" {
    for_each = [for volume in var.availability_zones: {
      availability_zone = volume
    }]
    content {
      availability_zone = placement.value.availability_zone
    }
  }

  dynamic "block_device_mappings" {
    for_each = [for volume in var.additional_volumes: {
      device_name = lookup(volume, "device_name", "/dev/xvdf" )
      delete_on_termination = lookup(volume, "delete_on_termination", true)
      encrypted = lookup(volume, "encrypted", false)
      volume_size = lookup(volume, "volume_size", 60)
      volume_type = lookup(volume, "volume_type", "gp2")
    }]
    content {
      device_name = block_device_mappings.value.device_name
      ebs {
        delete_on_termination = block_device_mappings.value.delete_on_termination
        encrypted = block_device_mappings.value.encrypted
        volume_size = block_device_mappings.value.volume_size
        volume_type = block_device_mappings.value.volume_type
      }
    }
  }
}
variable security_groups {
  type = list(string)
  default = []
}

variable availability_zones {
  type = list(string)
}

variable additional_volumes {
  type = list(object({
    device_name = string
    delete_on_termination = bool
    encrypted = bool
    volume_size = string
    volume_type = string
  }))
  default = []
}
variable subnet_id {
  type = string
}

variable launch_template_tags {
  type = map(any)
}

variable launch_template_tag_specification_tags {
  type = map(any)
  default = {}
}

variable cpu_credits {
  default = "unlimited"
}

module common-ami-ecs_image {
  source = "../../ami/linux2_ecs"
}

data aws_iam_instance_profile "rp" {
  name = var.aws_instance_profile
}

variable aws_instance_profile {
  default = "ecsInstanceRoleDO"
}

variable aws_instance_type {
  default = "t3.medium"
}

variable image_id {
  default = ""
}

variable user_data {
  default = ""
}

variable user_data_base64 {
  default = ""
}

variable "key_name" {
  default = ""
}
output template_id {
  value = aws_launch_template.lt.id
}

output template_version {
  value = aws_launch_template.lt.latest_version
}
locals {
  image_id = length(var.image_id) > 0? var.image_id: module.common-ami-ecs_image.id
  user_data_base64 = length(var.user_data_base64) > 0? var.user_data_base64: base64encode(var.user_data)
}
