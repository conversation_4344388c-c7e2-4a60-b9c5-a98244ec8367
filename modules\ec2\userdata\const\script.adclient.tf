output adclient {
  value = templatefile("${path.module}/templates/adclient.sh", {
    base-env = module.common-prefix.base-env
    function = var.adclient-function
  })
}

output systemd_adclient {
  value = templatefile("${path.module}/templates/systemd_adclient.sh", {
    base-env = module.common-prefix.base-env == "dev" ? "stg" : module.common-prefix.base-env
    function = var.adclient-function
    tid = var.tid
  })
}

variable adclient-function {
  default = "app"
}
