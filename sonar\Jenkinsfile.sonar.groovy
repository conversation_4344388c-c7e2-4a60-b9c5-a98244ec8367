def IS_PR = 'false'
def branchName
        
pipeline {
    agent {
        node {
            label 'linux-docker'
        }
    }

    environment{
        SONAR_URL = 'https://mssonar.morningstar.com'
        PDOP_URL = 'https://msstash.morningstar.com/scm/do/pms.aws-service.pdopapi.git'
        SCRIPT_URL = 'https://msstash.morningstar.com/scm/do/pms.aws-service.script.git'
    }
    
    parameters {
        string(
            name: 'CHANGE_ID',
            defaultValue: '',
            description: 'The change ID, such as a pull request number, if supported; else unset.'
        )
        string(
            name: 'CHANGE_BRANCH',
            defaultValue: '',
            description: 'The name of the actual head on the source control system'
        )
        string(
            name: 'CHANGE_TARGET',
            defaultValue: '',
            description: 'The target or base branch to which the change could be merged'
        )
    }

    stages{
        stage('Save dockerfile from script') {
            steps {
                script {
                    sh ''' pwd && ls -Al '''
                    stash includes: 'sonar/Dockerfile', name: 'dockerfile'
                }
            }
        }
        stage('Checkout pdopapi') {
            steps {
                script {
                    // Extract the branch name from the environment variable provided by <PERSON>
                    if (params.CHANGE_BRANCH) {
                        branchName = params.CHANGE_BRANCH
                        echo "This is a Pull Request with ID: ${params.CHANGE_ID}"
                        IS_PR = 'true'
                    } else {
                        // Fallback to a default branch if not running in a pull request context
                        branchName = 'develop'
                        echo "This is a regular branch build"
                        IS_PR = 'false'
                    }
                    echo 'download source code ...'
                    git credentialsId: "${params.GIT_CREDENTIALS}", url: "${env.PDOP_URL}", branch: "${branchName}"

                    sh ''' pwd && ls -Al '''
                }
            }
        }

        stage('Build and test'){
            steps{
                unstash 'dockerfile'
                sh '''pwd && ls -Al'''
                script{
                    if (IS_PR == 'true'){
                        docker.build('maven-sonarscanner',
                        """-f ./sonar/Dockerfile \
                        --build-arg IS_PR=${IS_PR} \
                        --build-arg SONAR_HOST_URL=${env.SONAR_URL} \
                        --build-arg SONAR_TOKEN=${SONAR_TOKEN} \
                        --build-arg KEY=${CHANGE_ID} \
                        --build-arg BRANCH=${CHANGE_BRANCH} \
                        --build-arg BASE=${CHANGE_TARGET} \
                        --progress plain \
                        ."""
                    )
                    }
                    else{
                        docker.build('maven-sonarscanner',
                        """-f ./sonar/Dockerfile \
                        --build-arg SONAR_HOST_URL=${env.SONAR_URL} \
                        --build-arg SONAR_TOKEN=${SONAR_TOKEN} \
                        --build-arg BRANCH=${branchName} \
                        --progress plain \
                        ."""
                    )
                    }
                }
            }
        }
    }
}