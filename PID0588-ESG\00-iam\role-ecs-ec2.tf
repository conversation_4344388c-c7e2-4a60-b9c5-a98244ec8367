resource "aws_iam_role" "ecsInstanceRoleDO" {
  count = var.enable_role_ecsInstanceRoleDO? 1:0
  name_prefix = "ecsInstanceRoleDO"
  path = "/service-role/"
  assume_role_policy = module.common-iam-const.assume-role-policy-ec2
  tags = module.common-tags.default_iam_role_tags
}

resource "aws_iam_role_policy_attachment" "ecsInstanceRoleDO-permissions" {
  count = var.enable_role_ecsInstanceRoleDO? length(module.common-iam-const.iam-policies-ecs):0
  role = aws_iam_role.ecsInstanceRoleDO[0].name
  policy_arn = module.common-iam-const.iam-policies-ecs[count.index]
  provisioner "local-exec" {
    command = "sleep 15"
  }
}

resource "aws_iam_instance_profile" "ecsInstanceRoleDO" {
  count = var.enable_role_ecsInstanceRoleDO? 1:0
  name = "ecsInstanceRoleDO"
  role = aws_iam_role.ecsInstanceRoleDO[0].name
}

variable "enable_role_ecsInstanceRoleDO" {
  default = false
}
