data "aws_route53_zone" "dataapi-public" {
  name = var.aws_zone_name_dataapi
  private_zone = false
}
data "aws_vpc_endpoint" "data-agent" {
  vpc_id       = module.common-vpc.default_vpc
  service_name = "com.amazonaws.us-east-1.execute-api"
  filter {
    name = "tag:Name"
    values = [
    "portfolio-data-agent-esg"
    ]
  }
}

variable "aws_zone_name_dataapi" {}

resource "aws_route53_record" "cname-ondemand-job-api" {
  count = module.common-prefix.isDR || module.common-prefix.isNonProdDR ?0:1
  zone_id = data.aws_route53_zone.dataapi-public.id
  name = "ondemand-job-api${module.common-prefix._-env}"
  type = "CNAME"
  ttl = "300"
  records = [data.aws_vpc_endpoint.data-agent.dns_entry[0].dns_name]
}

