FROM amazoncorretto:8-alpine-jre
ARG NEWRLIC_ENV
ADD ARTIFACT_NAME-*.jar app.jar
RUN unzip app.jar -d /appTmp
RUN mkdir -p /opt/newrelic/java/
RUN mv /appTmp/BOOT-INF/classes/newrelic/* /opt/newrelic/java/
ENV JAVA_OPTS="$JAVA_OPTS -javaagent:/opt/newrelic/java/newrelic.jar"
# An example of setting a system property config
ENV JAVA_OPTS="$JAVA_OPTS -Dnewrelic.config.app_name=Bus0052-Ts01075-CloudPositionPortfolioAPI-$NEWRLIC_ENV"
# An example of setting an Environment variable config
ENV NEW_RELIC_LICENSE_KEY="1349627b6833d62f94faa84a4b4ccdb9a52ce797"
# Config to include the agent logs in Docker's stdout logging
ENV JAVA_OPTS="$JAVA_OPTS -Dnewrelic.config.log_file_name=STDOUT"

ENV MAX_TRANSACTION_SAMPLES_STORED=1000 \
MAX_EVENT_SAMPLES_STORED=1000 \
NEW_RELIC_SPAN_EVENTS_ENABLED=true \
NEW_RELIC_SPAN_EVENTS_MAX_SAMPLES_STORED=1000 

# Config to include the Tags 
ENV NEW_RELIC_LABELS="BSId:Bus0052;BSName:Direct;TSId:Ts01075;TSName:Cloud Position Portfolio API;environment:$NEWRLIC_ENV"
ENTRYPOINT exec java $JAVA_OPTS -Dmstar_environment=$MSTAR_ENV -Djava.security.egd=file:/dev/./urandom -jar /app.jar "$0" "$@"
