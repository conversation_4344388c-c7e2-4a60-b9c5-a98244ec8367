locals {
  s3_deploy_foler_name = upper(var.mstar_env) == "PROD"||upper(var.mstar_env) == "UAT"? "dcs-deploy-prod-tempfolder":"dcs-deploy-tempfolder"
}

module dcs_deploy_folder {
  source = "../../modules/s3/resource"
  mstar_env = "PROD"
  name= local.s3_deploy_foler_name
  s3_bucket_tags = module.dcs-tags.client0_s3_bucket_tags
  qa_catchall = local.qa_catchall
  name_prefix = ""
}

locals {
  qa_catchall = false
}
variable "mstar_env" {
}