resource aws_ecs_cluster client-portfolio-cluster {
  name = "${module.common-prefix.env-}client-portfolio-cluster"
  tags = module.pdop-tags.default_ecs_cluster_tags
}

module common-ecs-client-portfolio-cluster {
  source = "../../modules/ecs/logs"
  mstar_env = var.mstar_env
  name = "client-portfolio-cluster"
  service_name = ""
}

resource aws_cloudwatch_log_group client-portfolio-cluster {
  name = module.common-ecs-client-portfolio-cluster.log_group
  tags = module.pdop-tags.default_log_group_tags
  retention_in_days = var.client-portfolio-cluster-log_retention
}

variable client-portfolio-cluster-log_retention {
  default = 7
}