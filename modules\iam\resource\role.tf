module common-prefix {
  source = "../../prefix"
  mstar_env = var.mstar_env
}

variable enable_role {
  default = true
}

variable mstar_env {
}

variable use_env_prefix {
  default = true
}

locals {
  env- = var.use_env_prefix? module.common-prefix.env-: ""
}

resource aws_iam_role role_with_name_prefix {
  count = var.enable_role && var.use_role_name_prefix? 1:0
  name_prefix = trimspace(substr(format("%s%s%s                                ", local.env-, var.service_prefix, var.name), 0, 32))
  path = var.path
  assume_role_policy = local.assume_role_policy
  tags = var.iam_role_tags
  provisioner "local-exec" {
    command = "sleep ${var.sleep}"
  }
}

resource aws_iam_role role_with_exact_name {
  count = var.enable_role && !var.use_role_name_prefix? 1:0
  name = trimspace(substr(format("%s%s%s                                                                ", local.env-, var.service_prefix, var.name), 0, 64))
  path = var.path
  assume_role_policy = local.assume_role_policy
  tags = var.iam_role_tags
  provisioner "local-exec" {
    command = "sleep ${var.sleep}"
  }
}

locals {
  role = var.use_role_name_prefix?aws_iam_role.role_with_name_prefix: aws_iam_role.role_with_exact_name
  resource_name = var.enable_role?local.role[0].name:null
  resource_arn = var.enable_role?local.role[0].arn:null
  resource_id = var.enable_role?local.role[0].id:null
}

output arn {
  value = local.resource_arn
}

output id {
  value = local.resource_id
}

variable use_role_name_prefix {
  default = true
}

variable service_prefix {
  default = "ecs-"
}

variable path {
  default = "/portfolio-service/"
}

variable name {
}

variable assume_role_policy {
  default = ""
}

locals {
  assume_role_policy = length(var.assume_role_policy) > 0? var.assume_role_policy: module.common-iam-const.assume-role-policy-ecs-tasks
}

resource aws_iam_role_policy_attachment permissions {
  count = var.enable_role? length(var.policies):0
  role = local.resource_name
  policy_arn = var.policies[count.index]
  provisioner "local-exec" {
    command = "sleep ${var.sleep}"
  }
}

variable iam_role_tags {
  type = map(string)
}

variable policies {
  type = list(string)
  default = []
}

variable sleep {
  default = 15
}

module common-iam-const {
  source = "../const"
}

resource aws_iam_role_policy permissions {
  count = var.enable_role? length(var.policy): 0
  name_prefix = "${local.env-}${var.name}"
  role = local.resource_id
  policy = var.policy[count.index]
}

variable policy {
  type = list(string)
  default = []
}

output "is_permission_ready" {
  value = concat(aws_iam_role_policy.permissions, aws_iam_role_policy_attachment.permissions)
}