{
  "Effect": "Allow",
  "Principal": {
    "AWS": "BEGIN{}${aws_principals_s3crud}END{}"
  },
  "Action": [
    "s3:GetObject",
    "s3:GetObjectVersion",
    "s3:PutObject",
    "s3:PutObjectAcl",
    "s3:DeleteObject"
  ],
  "Resource": "BEGIN{}${s3_bucket_files_s3crud}END{}"
},
{
  "Effect": "Allow",
  "Principal": {
    "AWS": "BEGIN{}${aws_principals_s3crud}END{}"
  },
  "Action": "s3:ListBucket",
  "Resource": "BEGIN{}${s3_bucket}END{}"
}
