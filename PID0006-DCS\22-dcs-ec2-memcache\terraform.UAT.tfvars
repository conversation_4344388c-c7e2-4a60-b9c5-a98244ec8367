mstar_env = "UAT"
# mstar_region = "aws-us-east-1"
aws_vpc_selected = "vpc-74f67c12"

ec2_aws_availability_zone = "us-east-1d"

aws_security_groups = [
  "console",
  "private_app",
  "private_active_directory_client",
#   "private_cloudbees_non-prod",
#   "private_do_common_non-prod",
  "private_dcs_prod"
]

default_outposts_security_group = [
  "console",
  "private_app",
  "private_active_directory_client",
#   "private_cloudbees_non-prod",
#   "private_do_common_non-prod",
  "private_dcs_prod"
]

ec2_instance_count = 4

# Amazon Linux 2023 AMI 2023.7.20250414.0 x86_64 HVM kernel-6.1
image_id = "ami-0e449927258d45bc4"

aws_instance_type = "r6i.xlarge"

aws_zone_name_dataapi = "do41368.eas.morningstar.com"