#!/usr/bin/env bash

#echo "\#(o_o)#/"

sudo su
set -ex

echo "start to build dcs-worker-ads env:${base-env}">> /tmp/install.log

dnf install -y libnsl libssh2 cronie
ln -sf /lib64/libldap_r-2.4.so.2 /lib64/libldap-2.4.so.2

echo "update time zone...">> /tmp/install.log
export TZ=America/Chicago
ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

s3bucket="dcs-deploy-tempfolder"
if [[ "${base-env}" = "prod" ]] || [[ "${base-env}" = "uat" ]]; then
  s3bucket="dcs-deploy-prod-tempfolder"
fi

aws s3 cp s3://$s3bucket/ads_3x/commonlib.tar.gz . 2>>/tmp/install.log
echo "downloaded depends...">> /tmp/install.log

##upzip## to /data/common/lib
tar -xzvf commonlib.tar.gz 2>>/tmp/install.log
rm -f commonlib.tar.gz

##install##
#mkdir -p /data/common/lib/ 2>>/tmp/install.log
#mv data/common/lib /data/common/lib/ 2>>/tmp/install.log
echo "/data/common/lib" >>/etc/ld.so.conf
ldconfig
echo "installed depends...">> /tmp/install.log

##gen crontab##
cd -
TMP_OLD_CRON=oldcrontab
crontab -l > $TMP_OLD_CRON
echo "* * * * * sh /opt/mstar/storage/calculation2.1/work_pmsapp/bin/start.sh autocheck > /dev/null 2>&1" >> $TMP_OLD_CRON
echo "* * * * * sh /opt/mstar/storage/calculation2.1/work_pms_group/bin/start.sh autocheck > /dev/null 2>&1" >> $TMP_OLD_CRON
cat $TMP_OLD_CRON | crontab
rm -f $TMP_OLD_CRON
echo "generaled crontab...">> /tmp/install.log

##some setting
#keep min memory > 1G
echo "vm.min_free_kbytes = 1048576" >> /etc/sysctl.conf

mkdir -p /data/app_logs/workerd_v2.1_pmsapp_log
mkdir -p /data/app_logs/workerd_v2.1_pmsgroup_log
mkdir -p /data/app_logs/workerd_v2.1_pmsapp_data
mkdir -p /data/app_logs/workerd_v2.1_pmsgroup_data
mkdir -p /opt/mstar/storage/calculation2.1/work_pmsapp/bin
mkdir -p /opt/mstar/storage/calculation2.1/work_pms_group/bin
ln -sf /data/app_logs/workerd_v2.1_pmsapp_log /opt/mstar/storage/calculation2.1/work_pmsapp/log
ln -sf /data/app_logs/workerd_v2.1_pmsapp_data /opt/mstar/storage/calculation2.1/work_pmsapp/data
ln -sf /data/app_logs/workerd_v2.1_pmsgroup_log /opt/mstar/storage/calculation2.1/work_pms_group/log
ln -sf /data/app_logs/workerd_v2.1_pmsgroup_data /opt/mstar/storage/calculation2.1/work_pms_group/data


echo "install done.">> /tmp/install.log

