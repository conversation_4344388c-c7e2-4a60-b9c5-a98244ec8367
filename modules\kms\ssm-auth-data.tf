data aws_ssm_parameter auth-data-key {
  count = length(local.auth-data-key)
  name = local.auth-data-key[count.index]
}

output para-auth-data-key {
  value = data.aws_ssm_parameter.auth-data-key[*].arn
}

locals {
  auth-data-key = split(",", module.common-prefix.isPROD? var.prod-auth-data-key: module.common-prefix.isDR? var.nonprod-auth-data-key[0]: join(",", var.nonprod-auth-data-key))
}

variable prod-auth-data-key {
  default = "ondemand-auth-data"
}

variable nonprod-auth-data-key {
  type = list(string)
  default = [
    "ondemand-auth-data",
  ]
}
