data aws_security_group sg {
  count = length(var.aws_security_groups)
  name = var.aws_security_groups[count.index]
}

variable aws_security_groups {
  type = list(string)
}

variable default_batch_security_group {
  type = set(string)
  default = [
  ]
}

variable default_lb_security_group {
  type = set(string)
  default = [
  ]
}

variable "public_lb_security_group" {
  type = set(string)
  default = [
  ]
}

variable default_ec2_security_group {
  type = set(string)
  default = [
  ]
}

variable elastic_search_security_group {
  type = set(string)
  default = [
  ]
}

variable default_elasticache_security_group {
  type = set(string)
  default = [
  ]
}

variable alb_security_group {
  type = set(string)
  default = [
  ]
}

output default_batch_security_group {
  value = [for sg in data.aws_security_group.sg[*]: sg.id if contains(var.default_batch_security_group, sg.name)]
}

output default_lb_security_group {
  value = [for sg in data.aws_security_group.sg[*]: sg.id if contains(var.default_lb_security_group, sg.name)]
}

output "public_lb_security_group" {
  value = [for sg in data.aws_security_group.sg[*]: sg.id if contains(var.public_lb_security_group, sg.name)]
}

output default_ec2_security_group {
  value = [for sg in data.aws_security_group.sg[*]: sg.id if contains(var.default_ec2_security_group, sg.name)]
}

output default_elasticache_security_group {
  value = [for sg in data.aws_security_group.sg[*]: sg.id if contains(var.default_elasticache_security_group, sg.name)]
}

output default_alb_security_group {
  value = [for sg in data.aws_security_group.sg[*]: sg.id if contains(var.alb_security_group, sg.name)]
}

output default_elastic_search_security_group {
  value = [for sg in data.aws_security_group.sg[*]: sg.id if contains(var.elastic_search_security_group, sg.name)]
}
