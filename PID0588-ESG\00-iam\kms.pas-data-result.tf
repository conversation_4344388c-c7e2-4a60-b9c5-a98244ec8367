resource aws_kms_key pas-data-result-key {
  description = "KMS key for pas data result encryption"
  policy = module.common-iam-kms-policies.template_content
  tags = module.esg-tags.default_kms_tags
}

resource aws_kms_alias pas-data-result-key {
  name = "alias/portfolio-service/pas-data-result-key"
  target_key_id = aws_kms_key.pas-data-result-key.key_id
}

locals {
  nonprod-pas-data-result-key-principals = [
    module.common-caller-const.datasvc_client_portfolio_esg_pas_ondemand_stg,
    module.common-caller-const.datasvc_client_portfolio_esg_pas_ondemand_qa,
    module.common-caller-const.datasvc_client_portfolio_esg_pas_ondemand_batch_stg,
    module.common-caller-const.datasvc_client_portfolio_fixedincome_pas_ondemand_stg,
    module.common-caller-const.datasvc_client_portfolio_fixedincome_pas_ondemand_batch_stg,
    module.common-caller-const.datasvc_client_portfolio_carbon_pas_ondemand_stg,
    module.common-caller-const.datasvc_client_portfolio_carbon_pas_ondemand_batch_stg
  ]

  prod-pas-data-result-key-principals = [
    module.common-caller-const.datasvc_client_portfolio_esg_pas_ondemand_prod,
    module.common-caller-const.datasvc_client_portfolio_esg_pas_ondemand_batch_prod,
    module.common-caller-const.datasvc_client_portfolio_carbon_pas_ondemand_prod,
    module.common-caller-const.datasvc_client_portfolio_carbon_pas_ondemand_batch_prod,
    module.common-caller-const.datasvc_client_portfolio_fixedincome_pas_ondemand_prod,
    module.common-caller-const.datasvc_client_portfolio_fixedincome_pas_ondemand_batch_prod,
    module.common-caller-const.do_client_portfolio_s3_decrypt_prod

  ]

  dr-pas-data-result-key-principals = [
    module.common-caller-const.datasvc_client_portfolio_esg_pas_ondemand_dr,
    module.common-caller-const.datasvc_client_portfolio_esg_pas_ondemand_batch_dr,
    module.common-caller-const.datasvc_client_portfolio_carbon_pas_ondemand_dr,
    module.common-caller-const.datasvc_client_portfolio_carbon_pas_ondemand_batch_dr,
    module.common-caller-const.datasvc_client_portfolio_fixedincome_pas_ondemand_dr,
    module.common-caller-const.datasvc_client_portfolio_fixedincome_pas_ondemand_batch_dr
  ]

  pas-data-result-key-principals = split(",", module.common-prefix.isPROD?local.prod-dr-principals : join(",", distinct(local.nonprod-pas-data-result-key-principals)))
  prod-dr-principals = module.common-prefix.isDR?local.dr-principals : local.prod-principals
  dr-principals = join(",", distinct(local.dr-pas-data-result-key-principals))
  prod-principals = join(",", distinct(local.prod-pas-data-result-key-principals))
}

resource aws_kms_grant pas-data-result-key-grants {
  count = length(local.pas-data-result-key-principals)
  grantee_principal = local.pas-data-result-key-principals[count.index]
  key_id = aws_kms_key.pas-data-result-key.key_id
  operations = [
    "Encrypt",
    "Decrypt",
    "GenerateDataKey",
    "DescribeKey",
  ]
}