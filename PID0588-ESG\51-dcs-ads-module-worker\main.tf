module "common-prefix" {
  source = "../../modules/prefix"
  mstar_env = var.mstar_env
  prefix = local.name
}

module "common-vpc-sg" {
  source = "../../modules/vpc/sg"
  aws_security_groups = var.aws_security_groups
  default_ec2_security_group = var.default_ec2_security_group
}

module "common-vpc" {
  source = "../../modules/vpc"
  aws_vpc = var.aws_vpc
  aws_availability_zone = var.aws_availability_zone
}

module "common-ecs-logs" {
  source = "../../modules/ecs/logs"
  mstar_env = var.mstar_env
  name = local.cluster_name
  service_name = local.name
}

module "common-iam-const" {
  source = "../../modules/iam/const"
}

module "pcs-tags" {
  source = "../../modules/tags"
  mstar_env = var.mstar_env
  tag_SERVICEID = "ts00941"
}


module "common-tags" {
  source = "../../modules/tags"
  mstar_env = var.mstar_env
}

variable asg_tags {
  type = list(any)
  default = []
}

variable "aws_vpc" {
}

variable "aws_availability_zone" {
  type = list(string)
}

variable "aws_security_groups" {
  type = list(string)
}

variable "default_ec2_security_group" {
  type = list(string)
}

variable "mstar_env" {
}

