#!groovy

pipeline {
    parameters {
        choice(choices: 'STG\nQA\nDEV\nPROD\nUAT\nDR', description: 'for lambda in which environment?', name: 'deploy_env')
        string(name: 'GIT_REPO', defaultValue: 'https://msstash.morningstar.com/scm/do/pms.aws-service.script.git', description: 'the repo to fetch code')
        string(name: 'GIT_BRANCH', defaultValue: 'master', description: 'the branch to build')
        choice(name: 'ARTIFACT_GROUP', choices: '''
do
''', description: 'name of the ECR repository is like ARTIFACT_GROUP/ARTIFACT_NAME')
        string(name: 'ECS_TAG_VERSION', description: 'the version of jar stored in S3 bucket, also be used to deploy lambda')
        choice(name: 'NEXUS_ARTIFACT_REPOSITORY', choices: '''
DO-snapshots
DO-releases
''', description: 'repository of the artifact to retrieve from nexus')
        choice(name: 'NEXUS_ARTIFACT_GROUP', choices: '''
com.morningstar
''', description: 'groupId of the artifact to retrieve from nexus')
        choice(name: 'ARTIFACT_NAME', choices: '''
portfolio-data-agent-esg-lambda
''', description: 'name of the artifact to retrieve from nexus')
        string(name: 'NEXUS_ARTIFACT_VERSION', defaultValue: '', description: 'version of the artifact to retrieve from nexus, also as a docker image tag, like "1.29-SNAPSHOT"')
    }
    environment {
        NONPROD_ACCOUNT = '************'
        NONPROD_ROLE = 'DO-ENGR-Deploy'
        PROD_ACCOUNT = '************'
        PROD_ROLE = 'DO-ENGR-Deploy'
        DOCKER_SCRIPTS = "${WORKSPACE}/docker/scripts"
    }

    options {
        ansiColor('xterm')
        skipDefaultCheckout()
        skipStagesAfterUnstable()
    }

    agent { label 'awsjenklinux' }

    stages {

        stage('download source code') {
            steps {
                //
                echo 'print current workspace'
                echo "${WORKSPACE}"
                // cleanWs
                deleteDir()
                echo 'download source code ...'
                git credentialsId: '8d2dd645-3268-4220-bdae-14d5be0ae0eb', url: '${GIT_REPO}', branch: '${GIT_BRANCH}'
                stash includes: '**/*', name: 'build'
            }
        }

        stage('upload jar to s3') {
            steps {
                sh 'export AWS_REGION=us-east-1; sh ${DOCKER_SCRIPTS}/upload-jar-to-s3.sh'
            }
        }
    }
}