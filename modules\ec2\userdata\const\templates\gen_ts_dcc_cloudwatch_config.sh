#!/usr/bin/env bash

cat <<'EOF' > /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json
{
    "agent": {
        "run_as_user": "root",
        "metrics_collection_interval": 10,
        "logfile": "/opt/aws/amazon-cloudwatch-agent/logs/amazon-cloudwatch-agent.log"
    },
    "logs": {
        "logs_collected": {
            "files": {
                "collect_list": [{
                        "file_path": "/opt/mstar/storage/dcc4ts/dcc/log/ts.log",
                        "log_group_name": "/aws/ec2/dcslog",
                        "log_stream_name": "${base-env}_ts_log",
                        "timezone": "Local"
                    }
                ]
            }
        },
        "force_flush_interval": 15
    }
}
EOF
sudo /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -m ec2 -a stop
sudo /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -m ec2 -a start


