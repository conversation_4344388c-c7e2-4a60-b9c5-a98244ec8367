#!/usr/bin/env bash

#echo "\#(o_o)#/"

sudo su

echo "start to build dcs-fcgi env:${base-env}">> /tmp/install.log

echo "update time zone...">> /tmp/install.log
export TZ=America/Chicago
ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

s3bucket="dcs-deploy-tempfolder-ias"
if [[ "${base-env}" = "prod" ]] || [[ "${base-env}" = "uat" ]]; then
  s3bucket="dcs-deploy-prod-tempfolder-ias"
fi

aws s3 cp s3://$s3bucket/fcgi/common.tar.gz . 2>>/tmp/install.log
aws s3 cp s3://$s3bucket/fcgi/apache-2.4.41.tar.gz . 2>>/tmp/install.log
aws s3 cp s3://$s3bucket/fcgi/calculation_access.tar.gz . 2>>/tmp/install.log
echo "downloaded depends...">> /tmp/install.log

##upzip## to /data/common/lib
tar -xzvf common.tar.gz -C /data/ 2>>/tmp/install.log
tar -xzvf apache-2.4.41.tar.gz -C /usr/local/ 2>>/tmp/install.log
tar -xzvf calculation_access.tar.gz -C /data/ 2>>/tmp/install.log

rm -f common.tar.gz
rm -f apache-2.4.41.tar.gz
rm -f calculation_access.tar.gz

echo "/data/common/lib" >>/etc/ld.so.conf
ldconfig
echo "installed depends...">> /tmp/install.log

###update rc3,5###
cat>httpd<<EOF
#!/bin/bash
/usr/local/apache-2.4.41/bin/apachectl -k start
EOF
chmod +x httpd
mv httpd /etc/init.d/httpd 2>>/tmp/install.log
cd /etc/rc3.d/ && ln -s ../init.d/httpd S88httpd && cd - 2>>/tmp/install.log
cd /etc/rc5.d/ && ln -s ../init.d/httpd S88httpd && cd - 2>>/tmp/install.log

echo "setup rc3,5">>/tmp/install.log

##add log for splunk##
#apache
groupadd apache
useradd apache -g apache
mkdir -p /data/apache_log 2>>/tmp/install.log

##gen crontab##
cd -
TMP_OLD_CRON=oldcrontab
crontab -l > $TMP_OLD_CRON
echo "* * * * * /usr/local/apache-2.4.41/bin/check_httpd.sh > /dev/null 2>&1" >> $TMP_OLD_CRON
echo "50 23 * * * find /data/apache_log/ -mtime +7 -name \"*.*\" -exec rm -rf {} \; > /dev/null 2>&1" >> $TMP_OLD_CRON
echo "50 23 * * * find /var/log/audit -mtime +7 -type f -name 'audit*' -exec rm -f {} \; > /dev/null 2>&1" >> $TMP_OLD_CRON
cat $TMP_OLD_CRON | crontab
rm -f $TMP_OLD_CRON
echo "generaled crontab...">> /tmp/install.log

##change config##
IP=`hostname -i`
sed -i s/dndfcgprdap6001/$IP/g /usr/local/apache-2.4.41/htdocs/udm.html
sed -i s/dndfcgprdap6001/$IP/g /data/calculation_access/etc/business_monitor.xml
cd /usr/local/apache-2.4.41/logs
chown apache fastcgi; chgrp apache fastcgi
cd /lib64
ln -s libcrypto.so.10 libcrypto.so.6
ln -s libssl.so.10 libssl.so.6

#start
/usr/local/apache-2.4.41/bin/httpd -k start 2>>/tmp/install.log

echo "install done.">> /tmp/install.log

