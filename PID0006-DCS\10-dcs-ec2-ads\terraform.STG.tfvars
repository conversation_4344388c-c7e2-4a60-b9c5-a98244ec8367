mstar_env = "STG"
mstar_region = "aws-us-east-1"
aws_vpc_selected = "vpc-49ef652f"

ec2_aws_availability_zone = "us-east-1d"

aws_security_groups = [
  "console",
  "private_app",
  "private_active_directory_client",
  "private_cloudbees_non-prod",
  "private_do_common_non-prod",
  "private_dcs_non-prod",
]

default_outposts_security_group = [
 "console",
  "private_app",
  "private_active_directory_client",
  "private_cloudbees_non-prod",
  "private_do_common_non-prod",
  "private_dcs_non-prod",
]

ec2_instance_count = 5

#Morningstar Amazon Linux 2 - Minimal 2021-02-08
image_id = "ami-0f25da7619ea1f94a"

aws_instance_type = "r6i.xlarge"

# aws_subnet = "subnet-13f4c45a"