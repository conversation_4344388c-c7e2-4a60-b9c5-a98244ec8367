module common-prefix {
  source = "../../../prefix"
  mstar_env = var.mstar_env
  prefix = var.service
}

variable mstar_env {}

variable service {}

variable scheduling_strategy {
  default = "DAEMON"
}

resource aws_ecs_service service_ec2 {
  count = var.use_ec2 && var.enable_scaling? 1:0
  name = module.common-prefix.env-prefix
  cluster = data.aws_ecs_cluster.cluster.cluster_name
  task_definition = module.common-ecs-service-task.arn
  scheduling_strategy = var.scheduling_strategy
  desired_count = var.desired_count
  deployment_minimum_healthy_percent = var.deployment_minimum_healthy_percent
  network_configuration {
    security_groups = var.security_groups
    subnets = var.subnets
  }
  placement_constraints {
    type = "memberOf"
    expression = "attribute:service == ${var.service}"
  }
  lifecycle {
    ignore_changes = [
      desired_count,
    ]
  }
  tags = local.ecs_service_tags
}

resource aws_ecs_service service_ec2-no-scaling {
  count = var.use_ec2 && !var.enable_scaling? 1:0
  name = module.common-prefix.env-prefix
  cluster = data.aws_ecs_cluster.cluster.cluster_name
  task_definition = module.common-ecs-service-task.arn
  scheduling_strategy = var.scheduling_strategy
  desired_count = var.desired_count
  deployment_minimum_healthy_percent = var.deployment_minimum_healthy_percent
  network_configuration {
    security_groups = var.security_groups
    subnets = var.subnets
  }
  placement_constraints {
    type = "memberOf"
    expression = "attribute:service == ${var.service}"
  }
  tags = local.ecs_service_tags
}

resource aws_ecs_service service_fargate {
  count = !var.use_ec2 && var.enable_scaling? 1:0
  name = module.common-prefix.env-prefix
  launch_type = "FARGATE"
  cluster = data.aws_ecs_cluster.cluster.cluster_name
  task_definition = module.common-ecs-service-task.arn
  desired_count = var.desired_count
  network_configuration {
    security_groups = var.security_groups
    subnets = var.subnets
  }
  lifecycle {
    ignore_changes = [
      desired_count,
    ]
  }
  tags = local.ecs_service_tags
}

resource aws_ecs_service service_fargate-no-scaling {
  count = !var.use_ec2 && !var.enable_scaling? 1:0
  name = module.common-prefix.env-prefix
  launch_type = "FARGATE"
  cluster = data.aws_ecs_cluster.cluster.cluster_name
  task_definition = module.common-ecs-service-task.arn
  desired_count = var.desired_count
  network_configuration {
    security_groups = var.security_groups
    subnets = var.subnets
  }
  tags = local.ecs_service_tags
}

locals {
  service_resource = var.use_ec2? (!var.enable_scaling? aws_ecs_service.service_ec2-no-scaling: aws_ecs_service.service_ec2) : (!var.enable_scaling? aws_ecs_service.service_fargate-no-scaling: aws_ecs_service.service_fargate)
  cluster_name = local.service_resource[0].cluster
  service_name = local.service_resource[0].name
}

output cluster {
  value = local.cluster_name
}

output service {
  value = local.service_name
}

output resource_id {
  value = !var.enable_scaling? "": format("service/%s/%s", local.cluster_name, local.service_name)
}

variable use_ec2 {
  default = false
}

variable desired_count {
  default = 0
}

variable security_groups {
  type = list(string)
}

variable subnets {
  type = list(string)
}

variable deployment_minimum_healthy_percent {
  default = 50
}

variable enable_scaling {
  default = true
}

variable "ecs_service_tagging_enabled" {
  default = false
}

locals {
  ecs_service_tags = var.ecs_service_tagging_enabled? var.ecs_service_tags: {}
}

variable ecs_service_tags {
  type = map(string)
  default = {}
}
