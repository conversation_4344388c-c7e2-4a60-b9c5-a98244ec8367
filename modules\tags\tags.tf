variable tag_PID {}
variable tag_SERVICEID {}
variable tag_TID {}
variable tag_BSID {}
variable tag_FUNCTION {}

module common-prefix {
  source = "../prefix"
  mstar_env = var.mstar_env
}

variable mstar_env {
}

locals {
  tags = {
    PID = var.tag_PID
    ENVIRONMENT = module.common-prefix.base-env
    SERVICEID = var.tag_SERVICEID
    TID	= var.tag_TID
    BSID = var.tag_BSID
    FUNCTION = var.tag_FUNCTION

  }
  ec2_tags = [
    {
      key = "PID"
      value = var.tag_PID
      propagate_at_launch = true
    },
    {
      key = "ENVIRONMENT"
      value = module.common-prefix.base-env
      propagate_at_launch = true
    },
    {
      key = "SERVICEID"
      value = var.tag_SERVICEID
      propagate_at_launch = true
    },
    {
      key = "TID"
      value = var.tag_TID
      propagate_at_launch = true
    },
    {
      key = "BSID"
      value = var.tag_BSID
      propagate_at_launch = true
    },
    {
      key = "FUNCTION"
      value = var.tag_FUNCTION
      propagate_at_launch = true
    },
  ]
}

output default_e2only_tags {
  value = local.tags
}

output default_dynamoDB_tags {
  value = local.tags
}

output default_ec2_tags {
  value = local.ec2_tags
}

output default_asg_tags {
  value = local.ec2_tags
}

output default_launch_template_tags {
  value = local.tags
}

output default_security_group_tags {
  value = local.tags
}

output default_kms_tags {
  value = local.tags
}

output default_certificate_tags {
  value = local.tags
}

output default_batch_environment_tags {
  value = local.tags
}

output default_elasticache_tags {
  value = local.tags
}

output default_lambda_tags {
  value = local.tags
}

output default_lb_tags {
  value = local.tags
}

output default_lb_target_group_tags {
  value = local.tags
}

output default_log_group_tags {
  value = local.tags
}

output default_s3_bucket_tags {
  value = local.tags
}

output default_ecs_cluster_tags {
  value = local.tags
}

output default_ecr_tags {
  value = local.tags
}

output default_ecs_service_tags {
  value = local.tags
}

output default_ecs_task_tags {
  value = local.tags
}

output client0_s3_bucket_tags {
  value = merge(local.tags, tomap({"CK"="Client0"}))
}

output client1_s3_bucket_tags {
  value = merge(local.tags, tomap({"CK"="Client1"}))
}

output default_sqs_tags {
  value = local.tags
}

output default_elasticsearch_tags {
  value = local.tags
}

output default_iam_role_tags {
  value = local.tags
}

output default_secret_tags {
  value = local.tags
}

output default_ssm_parameter_tags {
  value = local.tags
}

output default_cloudwatch_event_rule_tags {
  value = local.tags
}

output default_iam_role_tags2 {
  value = local.tags
}

output default_cloudwatch_metric_alarm_tags {
  value = local.tags
}

output default_sns_tags {
  value = local.tags
}

output default_api_gateway_tags {
  value = local.tags
}