mstar_env = "DR"
aws_region = "us-west-2"
aws_vpc_selected = "vpc-20687c47"

ec2_aws_availability_zone = "us-west-2a"
special_name = "us-west-2a-private"

aws_security_groups = [
  "console",
  "private_app",
  "private_active_directory_client",
  "private_dcs_prod"
]

default_outposts_security_group = [
  "console",
  "private_app",
  "private_active_directory_client",
  "private_dcs_prod"
]

ec2_instance_count = 1

# Amazon Linux 2023 AMI
image_id = "ami-05ee755be0cd7555c"
key_name = "dcs_dr"

aws_instance_type = "r6i.xlarge"

aws_zone_name_dataapi = "do41368.eas.morningstar.com"
