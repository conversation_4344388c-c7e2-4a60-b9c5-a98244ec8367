mstar_env       = "STG"
mstar_region    = "aws-us-east-1"
aws_vpc_outpost = "vpc-06ed9332a4992506f"

ec2_aws_availability_zone = "us-east-1c"


additional_volumes = [
  {
    device_name           = "/dev/xvda"
    delete_on_termination = true
    encrypted             = false
    volume_size           = 50
    volume_type           = "gp2"
  }
]

ec2_instance_count = 2

#Morningstar Amazon Linux 2 - Minimal 2021-02-08
image_id = "ami-0b10d2a25d66f56b2"
