module "common-iam" {
  source = "../../modules/iam/resource"
  mstar_env = var.mstar_env
  name = "portfolio-data-agent-esg-role"
  service_prefix = ""
  use_role_name_prefix = false
  policies = distinct(concat(module.common-iam-const.iam-policies-ecsTask, module.common-iam-const.iam-policies-elasticache-readonly))
  policy = list(module.common-iam-policies.template_content)
  iam_role_tags = module.esg-tags.default_iam_role_tags
}

module "common-iam-policies" {
  source = "../../modules/iam/policies"
  templates = [
    "kms",
    "s3",
    "s3crud",
  ]
  vars = {
    kms_key_arns = jsonencode(list(local.key_arn))
    input_buckets = jsonencode(list(local.s3_pas_esg_results_arn, local.s3_pas_fi_results_arn))
    input_bucket_files = jsonencode(list("${local.s3_pas_esg_results_arn}/*", "${local.s3_pas_fi_results_arn}/*"))
    output_buckets = jsonencode(list(local.s3_pas_holding_arn,local.s3_pas_esg_results_arn))
    output_bucket_files = jsonencode(list("${local.s3_pas_holding_arn}/*","${local.s3_pas_esg_results_arn}/*"))
  }
}

locals {
  kms_region = module.common-prefix.isPROD || module.common-prefix.isUAT? local.prod_kms_region:local.nonprod_kms_region
  prod_kms_region = module.common-prefix.isDR? "${var.dr_region}:${var.datasvc_prod}":"${var.default_region}:${var.datasvc_prod}"
  nonprod_kms_region = module.common-prefix.isDR? "${var.dr_region}:${var.datasvc_nonprod}":"${var.default_region}:${var.datasvc_nonprod}"
  key_arn = "arn:aws:kms:${local.kms_region}:key/${local.kms_id}"
  kms_id = module.common-prefix.isPROD || module.common-prefix.isUAT? local.prod_kms_id:local.nonprod_kms_id
  prod_kms_id =  module.common-prefix.isDR? var.para_prod_dr_kms_id:var.para_prod_kms_id
  nonprod_kms_id = var.para_nonprod_kms_id
  s3_pas_holding_arn = "arn:aws:s3:::${local.bucket_prefix-}mstar-pas-holdings"
  s3_pas_esg_results_arn = "arn:aws:s3:::${local.bucket_prefix-}mstar-pas-esg-results"
  s3_pas_fi_results_arn = "arn:aws:s3:::${local.bucket_prefix-}mstar-pas-fixedincome-results"
  bucket_prefix- = local.isQA? "stg-": local._bucket_prefix-
  _bucket_prefix- = module.common-prefix.isUAT? "":module.common-prefix.env-
  isQA = module.common-prefix.ENV == "QA"
}

variable default_region {
  default = "us-east-1"
}

variable dr_region {
  default = "us-west-2"
}

variable datasvc_prod {
  default = "062740692535"
}

variable datasvc_nonprod {
  default = "187914334366"
}

variable para_prod_dr_kms_id {
  default = "a0e2bee4-d10f-4a8e-b9f6-b00c276fc815"
}

variable para_prod_kms_id {
  default = "7361aa94-87fe-4a1a-a817-edbf2ccad470"
}

variable para_nonprod_kms_id {
  default = "1df59320-2d92-4b59-90bf-953020d4d504"
}




