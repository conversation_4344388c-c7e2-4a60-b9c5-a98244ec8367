resource aws_ecs_service portfolio-data-agent-service {
  name = module.common-prefix.env-prefix
  cluster = "${module.common-prefix.env-}${local.cluster_name}"
  launch_type = "FARGATE"
  task_definition = module.common-ecs-task.arn

  desired_count = var.portfolio-data-agent_desired > 0? var.portfolio-data-agent_desired:0

  load_balancer {
    target_group_arn = aws_lb_target_group.lb-group-pcs.arn
    container_name = module.common-prefix.env-prefix
    container_port = 80
  }
  health_check_grace_period_seconds = 300

  network_configuration {
    security_groups = distinct(concat(module.common-vpc-sg.default_ec2_security_group, list(data.aws_security_group.adclient.id)))

    subnets = module.common-vpc.default_private_subnet_ids
  }

  lifecycle {
    ignore_changes = [
      desired_count,
    ]
  }
}

data "aws_security_group" "adclient" {
  name = "private_active_directory_client"
}

variable portfolio-data-agent_desired {
  default = 2
}

variable "ecs_tag_version" {}