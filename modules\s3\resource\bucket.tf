module common-prefix {
  source = "../../prefix"
  mstar_env = var.mstar_env
}

variable mstar_env {
}

resource aws_s3_bucket_public_access_block bucket {
  count = local.enable_bucket? 1:0
  bucket = local.resource_id
  block_public_acls = true
  block_public_policy = true
  ignore_public_acls = true
  restrict_public_buckets = true
  depends_on = [
    aws_s3_bucket.bucket
  ]
}

resource aws_s3_bucket bucket {
  count = local.enable_bucket? 1:0
  bucket = "${module.common-prefix.env-}${local.name}"
  tags = var.s3_bucket_tags
}

variable name {
}

variable name_prefix {
  default = "mstar-dwm-"
}

variable qa_catchall {
  default = true
}

variable s3_bucket_tags {
  type = map(string)
}

locals {
  catchall_enabled = var.qa_catchall && (module.common-prefix.ENV == "QA" || module.common-prefix.ENV == "DEV")
  enable_bucket = !local.catchall_enabled
  resource = aws_s3_bucket.bucket
  resource_arn = local.resource[0].arn
  resource_id = local.resource[0].id
}

output arn {
  value = local.resource_arn
}

output id {
  value = local.resource_id
}

locals {
  name = "${var.name_prefix}${var.name}"
}

output enable_bucket {
  value = local.enable_bucket
}
