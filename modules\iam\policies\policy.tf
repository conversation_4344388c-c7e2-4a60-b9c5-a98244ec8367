variable templates {
  type = list(string)
}

variable vars {
  type = map(string)
  default = {}
}

locals {
  templates = compact(distinct(split(",", join(",", var.templates))))
}

data template_file policy-document {
  count = length(local.templates)
  template = replace(replace(file("${path.module}/templates/${local.templates[count.index]}.json"), "\"BEGIN{}", ""), "END{}\"", "")
  vars = var.vars
}

data template_file wrapper {
  template = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
     $${permission_entries}
   ]
}
EOF
  vars = {
    permission_entries = join(",", data.template_file.policy-document[*].rendered)
  }
}

output template_content {
  value = data.template_file.wrapper.rendered
}
