mstar_env = "UAT"
mstar_region = "aws-us-east-1"
aws_vpc = "vpc-74f67c12"
aws_security_groups = [
  "console",
  "private_app",
  "private_db",
  "public",
  "private_active_directory_client",
  "do_web_server_prod_outposts",
  "private_app_prod_outposts",
  "private_os_services_prod_outposts",
  "private_web_prod_outposts",
  "private_db_prod_outposts",
  "private_internet_outpost",
  "private_dcs_prod_outposts",
]
aws_availability_zone = [
  "us-east-1a",
  "us-east-1b",
  "us-east-1d",
]
default_elasticache_security_group = [
  "console",
  "private_app",
]
default_ec2_security_group = [
  "console",
  "private_app",
]
default_lb_security_group = [
  "private_app",
]
public_lb_security_group = [
  "public",
]

default_outposts_security_group = [
  "do_web_server_prod_outposts",
  "private_app_prod_outposts",
  "private_os_services_prod_outposts",
  "private_web_prod_outposts",
  "private_internet_outpost",
  "private_dcs_prod_outposts",
]

default_tscache_outposts_security_group = [
  "do_web_server_prod_outposts",
  "private_app_prod_outposts",
  "private_os_services_prod_outposts",
  "private_web_prod_outposts",
  "private_db_prod_outposts",
  "private_internet_outpost",
]
aws_zone_name_dataapi = "do41368.eas.morningstar.com."
ondemand-job-db-server=[
  "do-ondemand-job-db.cgwbebaec0nj.us-east-1.rds.amazonaws.com"
]