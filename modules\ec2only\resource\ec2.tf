resource aws_instance dcs-ec2 {
  count = var.ec2_count
  ami = local.image_id
  availability_zone = var.availability_zone
  instance_type = var.aws_instance_type
  ebs_optimized = replace(var.aws_instance_type, "/^t2/", "") == var.aws_instance_type
  iam_instance_profile = var.aws_instance_profile

  credit_specification {
    cpu_credits = var.cpu_credits
  }

  user_data = join("\n",tolist([replace(module.common-ec2-userdata-const.refresh_ip_to_s3,"/%.*%/",length(var.farm-name)==0?"${var.name-prefix}_${count.index+1}":"${var.name-prefix}_${var.farm-name}_${count.index+1}"),var.user_data,local.user_script_linux2]))
  subnet_id = var.subnet_id
  vpc_security_group_ids = var.security_groups
  key_name = var.key_name
  monitoring = true


  tags = merge(var.ec2_tags, tomap({"Name" = length(var.farm-name)==0?"${var.name-prefix}_${count.index+1}":"${var.name-prefix}_${var.farm-name}_${count.index+1}"}))

  dynamic "root_block_device" {
    for_each = [for volume in var.additional_volumes: {
      delete_on_termination = lookup(volume, "delete_on_termination", true)
      volume_size = lookup(volume, "volume_size", 60)
      volume_type = lookup(volume, "volume_type", "gp2")
    }]
    content {
      delete_on_termination = root_block_device.value.delete_on_termination
      volume_size = root_block_device.value.volume_size
      volume_type = root_block_device.value.volume_type
    }
  }
}

resource "aws_ssm_association" "mstar_base" {
  count = var.ec2_count > 0 && var.ssm_document_arn != null ? 1 : 0
  name = var.ssm_document_arn
  association_name = "${var.name-prefix}-mstar-base"
  apply_only_at_cron_interval = true
  schedule_expression = "at(${formatdate("YYYY-MM-DD'T'hh:mm", timestamp())})"  # only run once
  wait_for_success_timeout_seconds = 15 * 60
  targets {
    key    = "InstanceIds"
    values = aws_instance.dcs-ec2[*].id
  }

  depends_on = [ aws_instance.dcs-ec2 ]
}

variable security_groups {
  type = list(string)
  default = []
}

variable name-prefix {
  type = string
  default = "dcs"
}
variable farm-name {
  type = string
  default = ""
}
variable availability_zone {
  type = string
}

variable additional_volumes {
  type = list(object({
    device_name = string
    delete_on_termination = bool
    encrypted = bool
    volume_size = string
    volume_type = string
  }))
  default = [
    {
      device_name = "/dev/xvda"
      delete_on_termination = true
      encrypted = false
      volume_size = 60
      volume_type = "gp2"
    }
  ]
}

variable ec2_count {

}

variable subnet_id {
  type = string
}

variable ec2_tags {
  type = map(any)
}

variable cpu_credits {
  default = "unlimited"
}

module common-ami-ecs_image {
  source = "../../ami/linux2"
}

data aws_iam_instance_profile "rp" {
  name = var.aws_instance_profile
}

variable aws_instance_profile {
  default = "do-3x-application-servers"
}

variable aws_instance_type {
  default = "t3.medium"
}

variable image_id {
  default = ""
}

variable user_data_base64 {
  default = ""
}

variable "key_name" {
  default = "do-non-prod"
}

variable "user_data" {
  default = ""
}

output private_name_ips {
  value = zipmap(aws_instance.dcs-ec2[*].tags["Name"],aws_instance.dcs-ec2[*].private_ip)
}

output instance_ids {
  value = aws_instance.dcs-ec2[*].id
}

locals {
  image_id = length(var.image_id) > 0? var.image_id: module.common-ami-ecs_image.id
}
