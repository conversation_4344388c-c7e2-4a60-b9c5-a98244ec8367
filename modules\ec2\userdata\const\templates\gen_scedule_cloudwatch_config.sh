#!/usr/bin/env bash

cat <<'EOF' > /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json
{
    "agent": {
        "run_as_user": "root",
        "metrics_collection_interval": 10,
        "logfile": "/opt/aws/amazon-cloudwatch-agent/logs/amazon-cloudwatch-agent.log"
    },
    "logs": {
        "logs_collected": {
            "files": {
                "collect_list": [{
                        "file_path": "//data/app_logs/dcs_v2.1_schedule_log/audit.log",
                        "log_group_name": "/aws/ec2/dcslog",
                        "log_stream_name": "${base-env}_audit_log",
                        "timezone": "Local"
                    },
                    {
                        "file_path": "//data/app_logs/dcs_v2.1_schedule_log/scheduler.log",
                        "log_group_name": "/aws/ec2/dcslog",
                        "log_stream_name": "${base-env}_scheduler_log",
                        "timezone": "Local"
                    }
                ]
            }
        },
        "force_flush_interval": 15
    }
}
EOF
sudo /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -m ec2 -a stop
sudo /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl -m ec2 -a start



