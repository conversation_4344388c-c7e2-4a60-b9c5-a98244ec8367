module "common-vpc-sg" {
  source = "../../modules/vpc/sg"
  aws_security_groups = var.aws_security_groups
  default_ec2_security_group = var.default_ec2_security_group
}

module "common-vpc" {
  source = "../../modules/vpc"
  aws_vpc = var.aws_vpc
  aws_availability_zone = var.aws_availability_zone
}

module "common-java" {
  source = "../../modules/java"
  java-options = concat(var.java-options, local.java-options-local)
}

variable "java-options" {
  type = list(string)
  default = [
  ]
}

variable "mstar_region" {
  default = ""
}

locals {
  name = "portfolio-data-agent-esg-test-destroy"
  cluster_name = "do-portfolio"
  repo_name = "do/portfolio-data-agent-esg"
  java-options-local = [
    "-Dmstar_environment=${module.common-prefix.env}",
    "-Dmstar_region=${var.mstar_region}",
  ]
}

module "common-prefix" {
  source = "../../modules/prefix"
  mstar_env = var.mstar_env
  prefix = local.name
}

module "common-iam-const" {
  source = "../../modules/iam/const"
}

module "common-s3-alias" {
  source = "../../modules/s3/alias"
  mstar_env = var.mstar_env
}

variable "mstar_env" {
}

variable "aws_vpc" {
}

variable "aws_availability_zone" {
  type = list(string)
}

variable "aws_security_groups" {
  type = list(string)
}

variable "default_ec2_security_group" {
  type = list(string)
}

variable "aws_zone_name_dataapi" {
}

variable "default_lb_security_group" {
  type = list(string)
}