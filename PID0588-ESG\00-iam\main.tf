module "common-tags" {
  source = "../../modules/tags"
  mstar_env = var.mstar_env
  tag_PID = "PID0588"
}

module "common-prefix" {
  source = "../../modules/prefix"
  mstar_env = var.mstar_env
}

module "common-iam-const" {
  source = "../../modules/iam/const"
}

variable "mstar_env" {
}

module "common-caller" {
  source = "../../modules/caller"
}


module "common-caller-const" {
  source = "../../modules/caller/const"
}

module common-iam-kms-policies {
  source = "../../modules/iam/kms-policies"
  templates = [
    "root",
  ]
  vars = {
    account_id = module.common-caller.account_id
  }
}
