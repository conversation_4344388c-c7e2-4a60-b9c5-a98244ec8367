#!/usr/bin/env bash

if [ "x${deploy_env^^}" = "xPROD" ] || [ "x${deploy_env^^}" = "xDR" ] || [ "x${deploy_env^^}" = "xUAT" ]; then
  source ./assume_role_upload.sh ${PROD_ACCOUNT} ${PROD_ROLE}
else
  source ./assume_role_upload.sh ${NONPROD_ACCOUNT} ${NONPROD_ROLE}
fi

set +x
$(aws ecr get-login --region ${AWS_REGION} | sed 's/-e none//')

echo "prepare artifacts jar to s3"
rm -rf artifactsjar
mkdir artifactsjar
cd artifactsjar

NEXUS_BASE=https://msnexus.morningstar.com/
if [[ ${NEXUS_ARTIFACT_VERSION} = *"SNAPSHOT"* ]]; then
  METADATA_URL="${NEXUS_BASE}content/repositories/${NEXUS_ARTIFACT_REPOSITORY}/${NEXUS_ARTIFACT_GROUP//.//}/${ARTIFACT_NAME}/${NEXUS_ARTIFACT_VERSION}/maven-metadata.xml"
  echo "download ${METADATA_URL}"
  TIMESTAMP_VERSION=$(curl -f -sS "${METADATA_URL}" | tr -d ' \n' | grep -P '>jar</extension><value>\K[^<]+' -o)
  echo "Latest ${NEXUS_ARTIFACT_VERSION} artifact version = ${TIMESTAMP_VERSION}"
else
  TIMESTAMP_VERSION="${NEXUS_ARTIFACT_VERSION}"
fi

ARTIFACT_JAR="${ARTIFACT_NAME}-${TIMESTAMP_VERSION}.jar"
ARTIFACT_URL="${NEXUS_BASE}repository/${NEXUS_ARTIFACT_REPOSITORY}/${NEXUS_ARTIFACT_GROUP//.//}/${ARTIFACT_NAME}/${NEXUS_ARTIFACT_VERSION}/${ARTIFACT_JAR}"
echo "download ${ARTIFACT_URL}"
curl -sS -f -O "${ARTIFACT_URL}"

set +x
$(aws ecr get-login --region ${AWS_REGION} | sed 's/-e none//')

if [ "x${deploy_env^^}" = "xPROD" ] || [ "x${deploy_env^^}" = "xUAT" ]; then
   S3_BUCKET_NAME=prod-codedeploy-data-agent
elif [ "x${deploy_env^^}" = "xDR" ]; then
   S3_BUCKET_NAME=dr-codedeploy-data-agent
else
   S3_BUCKET_NAME=nonprod-codedeploy-data-agent
fi

if [ -z "${S3_BUCKET_NAME}" ]; then
  echo "no bucket name found"
  exit 1
fi

aws s3 cp ${ARTIFACT_JAR} s3://${S3_BUCKET_NAME}/$(echo "${deploy_env}" | tr 'A-Z' 'a-z')/${ECS_TAG_VERSION}/${ARTIFACT_NAME}.jar
