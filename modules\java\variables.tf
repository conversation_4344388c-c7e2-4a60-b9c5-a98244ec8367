variable base-options {
  type = list(string)
  default = [
    "-XX:+UseStringDeduplication",
    "-XX:MaxMetaspaceSize=200m",
  ]
}

variable g1-options {
  type = list(string)
  default = [
    "-XX:+UseG1GC",
    "-XX:MaxGCPauseMillis=3000",
    "-XX:+ExitOnOutOfMemoryError",
  ]
}

variable ms-options {
  type = list(string)
  default = [
    "-Dserde_codec_tag=deflate",
  ]
}

variable cg-options {
  type = list(string)
  default = [
    "-XX:+UnlockExperimentalVMOptions"
  ]
}

variable gc-log-summary {
  type = list(string)
  default = [
    "-XX:+PrintAdaptiveSizePolicy",
    "-XX:NativeMemoryTracking=summary",
  ]
}

variable gc-log-detail {
  type = list(string)
  default = [
    "-XX:+PrintGC",
    "-XX:+PrintGCDetails",
    "-XX:+PrintStringDeduplicationStatistics",
  ]
}

variable java-options {
  type = list(string)
  default = []
}

variable min-heap {
  default = ""
}

variable max-heap {
  default = ""
}

locals {
  min-heap = length(var.min-heap) > 0? format("-Xms%s", var.min-heap): ""
  max-heap = length(var.max-heap) > 0? format("-Xmx%s", var.max-heap): ""
  heap-options = compact(list(local.min-heap, local.max-heap))
  java-options = distinct(concat(local.heap-options, var.java-options, var.ms-options, var.g1-options, var.cg-options, var.base-options))
  gc-summary = distinct(concat(local.java-options, var.gc-log-summary))
  gc-detail = distinct(concat(local.gc-summary, var.gc-log-detail))
}

output java-options {
  value = local.java-options
}

output gc-summary {
  value = local.gc-summary
}

output gc-detail {
  value = local.gc-detail
}
