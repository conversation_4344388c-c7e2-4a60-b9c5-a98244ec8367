locals {
  iam-policies-lambda-basic = [
    "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
  ]
  iam-policies-lambda-dynamoDB = [
    "arn:aws:iam::aws:policy/service-role/AWSLambdaDynamoDBExecutionRole",
  ]
  iam-policies-lambda-kinesis = [
    "arn:aws:iam::aws:policy/service-role/AWSLambdaKinesisExecutionRole",
  ]
  iam-policies-lambda-vpc = [
    "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole",
  ]
  iam-policies-elasticache-readonly = [
    "arn:aws:iam::aws:policy/AmazonElastiCacheReadOnlyAccess",
  ]
  iam-policies-lambda-role = [
    "arn:aws:iam::aws:policy/service-role/AWSLambdaRole"
  ]

  iam-policies-rds = [
    "arn:aws:iam::aws:policy/service-role/AWSLambdaRole"
  ]
}
output iam-policies-rds {
  value = local.iam-policies-rds
}

output iam-policies-lambda-role {
  value = local.iam-policies-lambda-role
}

output iam-policies-elasticache-readonly {
  value = local.iam-policies-elasticache-readonly
}

output iam-policies-lambda-basic {
  value = local.iam-policies-lambda-basic
}

output iam-policies-lambda-dynamoDB {
  value = local.iam-policies-lambda-dynamoDB
}

output iam-policies-lambda-kinesis {
  value = local.iam-policies-lambda-kinesis
}

output iam-policies-lambda-vpc {
  value = local.iam-policies-lambda-vpc
}

locals {
  iam-policies-lambda-elasticache = distinct(concat(local.iam-policies-lambda-vpc, local.iam-policies-elasticache-readonly))
  iam-policies-lambda-kinesis-and-vpc = distinct(concat(local.iam-policies-lambda-kinesis, local.iam-policies-lambda-vpc))
  iam-policies-lambda-kinesis-and-elasticache = distinct(concat(local.iam-policies-lambda-kinesis-and-vpc, local.iam-policies-elasticache-readonly))
}

output iam-policies-lambda-elasticache {
  value = local.iam-policies-lambda-elasticache
}

output iam-policies-lambda-kinesis-and-vpc {
  value = local.iam-policies-lambda-kinesis-and-vpc
}

output iam-policies-lambda-kinesis-and-elasticache {
  value = local.iam-policies-lambda-kinesis-and-elasticache
}
