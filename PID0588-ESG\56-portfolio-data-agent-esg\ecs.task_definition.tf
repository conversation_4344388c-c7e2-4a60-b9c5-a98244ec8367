module "common-ecs-task" {
  source = "../../modules/ecs/resource/service_awsvpc/task"
  mstar_env = var.mstar_env
  service = local.name
  cluster = local.cluster_name
  role = module.common-iam.arn
  repository = local.repo_name
  ecs_tag_version = var.ecs_tag_version
  cpu = 2048
  memory = 9216
  ecs_task_tags = module.esg-tags.default_ecs_task_tags
  java_options = module.common-java.java-options
  portMappings = [
    {
      containerPort = 80
      hostPort = 80
      protocol = "tcp"
    }
  ]
}
