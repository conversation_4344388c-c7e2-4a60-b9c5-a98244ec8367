mstar_env       = "QA"
mstar_region    = "aws-us-west-2"
aws_vpc_outpost = "vpc-020883b4539820019"


ec2_aws_availability_zone = "us-west-2b"

aws_security_groups = [
  "private_app_non-prod_outposts",
  "private_os_services_non-prod_outposts",
  "private_do_web_non-prod_outposts",
  "private_db_non-prod_outposts",
  "private_internet_outpost",
  "private_dcs_non-prod_outposts",
  "private_do_common_non-prod_outposts",
]

default_outposts_security_group = [
  "private_app_non-prod_outposts",
  "private_os_services_non-prod_outposts",
  "private_do_web_non-prod_outposts",
  "private_db_non-prod_outposts",
  "private_internet_outpost",
  "private_dcs_non-prod_outposts",
  "private_do_common_non-prod_outposts",
]

special_name  = "us-west-2-private-outpost1-chicago"


ec2_instance_count = 2

#Morningstar Amazon Linux 2 - Minimal 2021-02-08
image_id = "ami-063c0b02dba1fe297"
