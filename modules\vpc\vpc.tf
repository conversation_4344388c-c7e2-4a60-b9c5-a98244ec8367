data aws_vpc default {
  id = var.aws_vpc
}

variable aws_vpc {
}

output default_vpc {
  value = data.aws_vpc.default.id
}

data aws_subnet_ids private_subnet {
  vpc_id = data.aws_vpc.default.id
  tags = {
    FUNCTION = "PRIVATE"
  }
}

output default_private_subnet_ids {
  value = sort(data.aws_subnet_ids.private_subnet.ids)
}

data aws_subnet_ids public_subnet {
  vpc_id = data.aws_vpc.default.id
  tags = {
    FUNCTION = "PUBLIC"
  }
}

output default_public_subnet_ids {
  value = sort(data.aws_subnet_ids.public_subnet.ids)
}
