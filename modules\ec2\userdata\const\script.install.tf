output install_TS_FCGI {
value = templatefile("${path.module}/templates/install_TS_FCGI.sh", {
base-env = module.common-prefix.base-env
})
}

output install_TS_DCC {
value = templatefile("${path.module}/templates/install_TS_DCC.sh", {
base-env = module.common-prefix.base-env
})
}

output install_dcs_common {
  value = templatefile("${path.module}/templates/install_dcs_common.sh", {
    efs_dns = var.efs_dns
  })
}

output install_dcs_worker_ads {
value = templatefile("${path.module}/templates/install_dcs_worker_ads.sh", {
base-env = module.common-prefix.base-env
})
}

output install_dcs_worker_ads_ias {
  value = templatefile("${path.module}/templates/install_dcs_worker_ads_ias.sh", {
    base-env = module.common-prefix.base-env
  })
}

output install_dcs_worker_att {
value = templatefile("${path.module}/templates/install_dcs_worker_att.sh", {
base-env = module.common-prefix.base-env
})
}

output install_dcs_worker_ccs {
  value = templatefile("${path.module}/templates/install_dcs_worker_ccs.sh", {
    base-env = module.common-prefix.base-env
  })
}

output enable_splunk_agent {
  value = templatefile("${path.module}/templates/enable_splunk_agent.sh", {
    base-env = module.common-prefix.base-env
  })
}

output install_dcs_fcgi {
  value = templatefile("${path.module}/templates/install_dcs_fcgi.sh", {
    base-env = module.common-prefix.base-env
  })
}

output install_dcs_fcgi_ias {
  value = templatefile("${path.module}/templates/install_dcs_fcgi_ias.sh", {
    base-env = module.common-prefix.base-env
  })
}

output install_dcs_schedule {
  value = templatefile("${path.module}/templates/install_dcs_schedule.sh", {
    base-env = module.common-prefix.base-env
  })
}

output install_dcs_schedule_ias {
  value = templatefile("${path.module}/templates/install_dcs_schedule_ias.sh", {
    base-env = module.common-prefix.base-env
  })
}

output install_dcs_worker_assetallocation {
  value = templatefile("${path.module}/templates/install_dcs_worker_assetallocation.sh", {
    base-env = module.common-prefix.base-env
  })
}

output install_dcs_worker_planning {
  value = templatefile("${path.module}/templates/install_dcs_worker_planning.sh", {
    base-env = module.common-prefix.base-env
  })
}

output mstar_patch_agent {
  value = templatefile("${path.module}/templates/mstar_patch_agent.sh", {
  })
}

output mstar_patch_agent_prod {
  value = templatefile("${path.module}/templates/mstar_patch_agent_prod.sh", {
  })
}

