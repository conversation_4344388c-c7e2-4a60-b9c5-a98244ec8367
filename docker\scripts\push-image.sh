#!/usr/bin/env bash

if [ "${deploy_env}" = "PROD" ]; then
  source ./assume_role.sh ${PROD_ACCOUNT} ${PROD_ROLE}
  ECR_URL="${PROD_ACCOUNT}.dkr.ecr.${AWS_REGION}.amazonaws.com"
elif [ "${deploy_env}" = "non-PROD" ]; then
  source ./assume_role.sh ${NONPROD_ACCOUNT} ${NONPROD_ROLE}
  ECR_URL="${NONPROD_ACCOUNT}.dkr.ecr.${AWS_REGION}.amazonaws.com"
fi

set +x
$(aws ecr get-login --region ${AWS_REGION} | sed 's/-e none//')

[ ! -z "${ARTIFACT_GROUP}" ] && [ ! -z "${ARTIFACT_NAME}" ] && (
  repository="${ARTIFACT_GROUP}/${ARTIFACT_NAME}"
  REMOTE_TAG="${ECR_URL}/${repository}:${NEXUS_ARTIFACT_VERSION}"
  echo "push ${ARTIFACT_NAME} to ${REMOTE_TAG}"
  docker tag "${ARTIFACT_NAME}" "${REMOTE_TAG}" &&
    docker push "${REMOTE_TAG}"
  docker rmi "${REMOTE_TAG}"
  # also push a "latest" version
  REMOTE_TAG="${ECR_URL}/${repository}:latest"
  echo "push ${ARTIFACT_NAME} to ${REMOTE_TAG}"
  docker tag "${ARTIFACT_NAME}" "${REMOTE_TAG}" &&
    docker push "${REMOTE_TAG}"
  docker rmi "${REMOTE_TAG}"
  # also push a tag to match task definition
  if [ "x${ECS_TAG_VERSION}" != "x" ]; then
    REMOTE_TAG="${ECR_URL}/${repository}:${ECS_TAG_VERSION}"
    echo "push ${ARTIFACT_NAME} to ${REMOTE_TAG}"
  docker tag "${ARTIFACT_NAME}" "${REMOTE_TAG}" &&
     docker push "${REMOTE_TAG}"
  docker rmi "${REMOTE_TAG}"
  fi
) || echo "skip ${ARTIFACT_GROUP}/${ARTIFACT_NAME}"