mstar_env       = "STG"
mstar_region    = "us-east-1"
aws_vpc_outpost = "vpc-0a6fb75fd2526c3ae"

ec2_aws_availability_zone = "us-east-1c"

special_name  = "us-east-1c-private"


ec2_instance_count = 5

#Morningstar Amazon Linux 2 - Minimal 2024-01-19
image_id = "ami-0688abfbf28490c22"

aws_vpc = "vpc-0a6fb75fd2526c3ae"
aws_security_groups = [
  "console",
  "private_web",
  "private_app",
]
aws_availability_zone = [
  "us-east-1a",
  "us-east-1b",
  "us-east-1c",
]
default_elasticache_security_group = [
  "console",
  "private_web",
  "private_app",
]
default_ec2_security_group = [
  "console",
  "private_web",
  "private_app",
]
default_lb_security_group = [
  "private_app",
]
public_lb_security_group = [
  "public",
]

default_tscache_ec2_security_group = [
  "console",
  "private_web",
  "private_app",
]

aws_zone_name_dataapi = "ias04bd6.easn.morningstar.com"

key_name = "abacus_linux_nonprod"
