resource aws_lb_listener lb-portfolio-setting {
  load_balancer_arn = aws_lb.lb-portfolio-setting.arn
  port = "80"
  protocol = "HTTP"
  default_action {
    target_group_arn = aws_lb_target_group.lb-group-portfolio-setting.arn
    type = "forward"
  }
}

resource aws_lb_listener https_lb_portfolio_setting_listener {
  load_balancer_arn = aws_lb.lb-portfolio-setting.arn
  port = "443"
  protocol = "HTTPS"
  ssl_policy = "ELBSecurityPolicy-2016-08"
  certificate_arn = aws_acm_certificate_validation.portfolio_setting_cert.certificate_arn
  default_action {
    target_group_arn = aws_lb_target_group.lb-group-portfolio-setting.arn
    type = "forward"
  }
}

resource aws_acm_certificate_validation portfolio_setting_cert {
  certificate_arn = aws_acm_certificate.portfolio_setting_cert.arn
  validation_record_fqdns = [for record in aws_route53_record.portfolio_setting_cert_validation : record.fqdn]
}

resource aws_route53_record portfolio_setting_cert_validation {
  for_each = {
  for dvo in aws_acm_certificate.portfolio_setting_cert.domain_validation_options : dvo.domain_name => {
    name = dvo.resource_record_name
    record = dvo.resource_record_value
    type = dvo.resource_record_type
  }}

  allow_overwrite = true
  name = each.value.name
  records = list(each.value.record)
  ttl = 60
  type = each.value.type
  zone_id = data.aws_route53_zone.zone_domain.id
}

data aws_route53_zone zone_domain {
  name = var.aws_zone_name_dataapi
  private_zone = false
}

resource aws_acm_certificate portfolio_setting_cert {
  domain_name = "portfolio-setting${module.common-prefix._-env}.${replace(var.aws_zone_name_dataapi, "/[.]$/", "")}"
  validation_method = "DNS"
  tags = module.pdop-tags.default_certificate_tags
  lifecycle {
    create_before_destroy = true
  }
}