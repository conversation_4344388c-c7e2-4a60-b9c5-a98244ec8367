#!/usr/bin/env bash

sudo su
set -ex

function get_tags() {
    TOKEN=$(curl -X PUT -s "http://169.254.169.254/latest/api/token" -H "X-aws-ec2-metadata-token-ttl-seconds: 21600")
    INSTANCE_ID=$(curl -s -H "X-aws-ec2-metadata-token: $TOKEN" http://169.254.169.254/latest/meta-data/instance-id)
    REGION="$(curl -s -H "X-aws-ec2-metadata-token: $TOKEN" http://169.254.169.254/latest/meta-data/placement/region)"
    TAGS_JSON=$(aws ec2 describe-tags --region "$REGION" --filters "Name=resource-id,Values=$INSTANCE_ID" --output json)
    NAME_TAG=$(echo "$TAGS_JSON" | jq -r '.Tags[] | select(.Key=="Name") | .Value')
    # ENV_TAG=$(echo "$TAGS_JSON" | jq -r '.Tags[] | select(.Key=="ENVIRONMENT") | .Value')
}
# get_tags

dnf install -y libxcrypt-compat libnsl

aws s3 sync s3://dcs-deploy-prod-tempfolder/dcs-ec2-common/ /tmp/dcs-ec2-common/

# fix splunk
rsync -a /tmp/dcs-ec2-common/aws_app_do-default-linux_inputs /opt/splunkforwarder/etc/apps/
pkill -f splunk
systemctl restart splunk

# fix SELinux
semanage fcontext -a -t bin_t '/opt/calculation_access/bin(/.*)?'
semanage fcontext -a -t lib_t '/opt/calculation_access/lib(/.*)?'
semanage fcontext -a -t lib_t '/opt/common/lib(/.*)?'
semanage fcontext -C -l # verify
restorecon -Rv /opt    # apply

# sync common libraries
mkdir -p /data/common/lib
tar -xzvf /tmp/dcs-ec2-common/common_lib.tar.gz -C /data/common/lib
chmod -R 777 /data/common

# fix permissions
mkdir -p /data/calculation_access
chmod -R 777 /data/calculation_access

# mount EFS
if [[ ! -z "$efs_dns" ]]; then
    mkdir -p /pms-data
    "$efs_dns:/ /pms-data nfs4 rsize=8192,wsize=8192,hard,timeo=600,retrans=2,noresvport,_netdev 0 0" >> /etc/fstab
    mount -a
fi

# set time zone to CDT, the `timedatectl set-timezone America/Chicago` command might fail sometimes
ln -sf /usr/share/zoneinfo/America/Chicago /etc/localtime