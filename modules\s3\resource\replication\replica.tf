variable s3_replication_bucket {
  default = ""
}

variable s3_replication_bucket_prefix {
  default = "dr-"
}

variable s3_replication_role {
  default = "s3-replication"
}

locals {
  _s3_replication_bucket = length(var.s3_replication_bucket_prefix) > 0 && !module.common-prefix.isDR ? trimspace(substr(format("%s%s%s                                                                ", module.common-prefix.env-, var.s3_replication_bucket_prefix, local.name), 0, 63)):""
  s3_replication_bucket = length(var.s3_replication_bucket) > 0? var.s3_replication_bucket: local._s3_replication_bucket
  enable_replication = local.enable_bucket && length(local.s3_replication_bucket) > 0 && length(var.s3_replication_role) > 0
  replica_arn = local.enable_replication? data.aws_s3_bucket.replica[0].arn:""
  replica_id = local.enable_replication? data.aws_s3_bucket.replica[0].id:""
  replication_role_arn = local.enable_replication? data.aws_iam_role.replication-role[0].arn:""
}

data aws_s3_bucket replica {
  count = local.enable_replication? 1:0
  bucket = local.s3_replication_bucket
  provider = aws.replication
}

output replica_arn {
  value = local.replica_arn
}

data aws_iam_role replication-role {
  count = local.enable_replication? 1:0
  name = var.s3_replication_role
}
