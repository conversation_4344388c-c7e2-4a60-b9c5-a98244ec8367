module "common-prefix" {
  source = "../../modules/prefix"
  mstar_env = var.mstar_env
  prefix = local.name
}

module "common-vpc-sg" {
  source = "../../modules/vpc/sg"
  aws_security_groups = var.aws_security_groups
  default_ec2_security_group = var.default_tscache_outposts_security_group
}

variable "aws_security_groups" {
  type = list(string)
}

variable "default_tscache_outposts_security_group" {
  type = list(string)
  default = []
}

variable "mstar_env" {
}

variable "aws_vpc" {
}

variable "ec2_aws_availability_zone" {
  type = string
}

data aws_vpc outpost {
  id = var.aws_vpc_outpost
}

variable aws_vpc_outpost {
}

variable special_name {
  default = "us-east-1-private-outpost1-dallas"
}

data "aws_subnet" "selected" {
  vpc_id = data.aws_vpc.outpost.id
  filter {
    name   = "tag:Name"
    values = [
      "${var.special_name}",
    ]
  }
}

output special_private_subnet_ids {
  value = data.aws_subnet.selected[*].id
}

