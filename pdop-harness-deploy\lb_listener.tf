resource aws_lb_listener lb-portfolio-service {
  load_balancer_arn = aws_lb.lb-portfolio-service.arn
  port = "80"
  protocol = "TCP"
  default_action {
    target_group_arn = aws_lb_target_group.lb-group-portfolio-service.arn
    type = "forward"
  }
}

resource aws_acm_certificate cert {
  domain_name = module.common-prefix.isPROD? "portfolio-data.${replace(var.aws_zone_name_dataapi, "/[.]$/", "")}":"portfolio-data${module.common-prefix._-env}.${replace(var.aws_zone_name_dataapi, "/[.]$/", "")}"
  validation_method = "DNS"
  tags = module.pdop-tags.default_certificate_tags
  lifecycle {
    create_before_destroy = true
  }
}

data aws_route53_zone zone_domain {
  name = var.aws_zone_name_dataapi
  private_zone = false
}

resource aws_route53_record cert_validation {
  for_each = {
  for dvo in aws_acm_certificate.cert.domain_validation_options : dvo.domain_name => {
    name = dvo.resource_record_name
    record = dvo.resource_record_value
    type = dvo.resource_record_type
  }}

  allow_overwrite = true
  name = each.value.name
  records = tolist([each.value.record])
  ttl = 60
  type = each.value.type
  zone_id = data.aws_route53_zone.zone_domain.id
}

resource aws_acm_certificate_validation cert {
  certificate_arn = aws_acm_certificate.cert.arn
  validation_record_fqdns = [for record in aws_route53_record.cert_validation : record.fqdn]
}

resource aws_lb_listener lb-portfolio-service-tls {
  load_balancer_arn = aws_lb.lb-portfolio-service.arn
  port = "443"
  protocol = "TLS"
  ssl_policy = "ELBSecurityPolicy-TLS-1-2-Ext-2018-06"
  certificate_arn = aws_acm_certificate_validation.cert.certificate_arn
  default_action {
    target_group_arn = aws_lb_target_group.lb-group-portfolio-service.arn
    type = "forward"
  }
}
