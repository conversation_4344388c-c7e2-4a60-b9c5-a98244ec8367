mstar_env       = "STG"
mstar_region    = "us-east-1"
aws_vpc_ec2 = "vpc-0a6fb75fd2526c3ae"


ec2_aws_availability_zone = "us-east-1c"

special_name  = "us-east-1c-private"
aws_vpc = "vpc-0a6fb75fd2526c3ae"
aws_security_groups = [
  "console",
  "private_web",
  "private_app",
]

default_ec2_security_group = [
  "console",
  "private_web",
  "private_app",
]


aws_zone_name_dataapi = "ias04bd6.easn.morningstar.com"


ec2_instance_count = 1

#Morningstar Amazon Linux 2 - Minimal 2024-01-19
image_id = "ami-0688abfbf28490c22"

key_name = "abacus_linux_nonprod"