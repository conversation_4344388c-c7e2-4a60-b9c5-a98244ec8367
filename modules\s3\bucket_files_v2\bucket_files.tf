locals {
  key-arn = data.template_file.bucket_arn[*].rendered
  key-prefix = data.template_file.file_prefix[*].rendered
  buckets = distinct(local.key-arn)
  bucket_files = distinct(formatlist("%s/%s*", local.key-arn, local.key-prefix))
}

variable s3-all-buckets {
  type = list(object({
    arn = string
    prefix = string
  }))
}

data template_file bucket_arn {
  count = length(var.s3-all-buckets)
  template = lookup(var.s3-all-buckets[count.index], "arn")
}

data template_file file_prefix {
  count = length(var.s3-all-buckets)
  template = lookup(var.s3-all-buckets[count.index], "prefix")
}

output buckets {
  value = local.buckets
}

output bucket_files {
  value = local.bucket_files
}

output buckets-json {
  value = jsonencode(local.buckets)
}

output bucket_files-json {
  value = jsonencode(local.bucket_files)
}
