module dcs_cloudwatch_schedule {
  source       = "../../modules/cloudwatch/resource"
  mstar_env    = var.mstar_env
  instance_ids = var.instance_ids
  name         = local.name
  name-prefix = module.common-prefix.env-prefix
  target_arn = var.target_arn
}

module "common-prefix" {
  source = "../../modules/prefix"
  mstar_env = var.mstar_env
  prefix = local.name
}

locals {
  name = "dcs_auto_stop_outposts_ec2"
}

variable "mstar_env" {
}

variable instance_ids {

}

variable target_arn {
  default = "arn:aws:events:us-east-1:146985796559:target/stop-instance"
}

variable target_role_arn {
  default = "arn:aws:iam::146985796559:role/AWS_Events_Actions_Execution"
}