resource aws_cloudwatch_metric_alarm esg-data-agent-minus-capacity-cpu-one {
  alarm_name = "${module.common-prefix.ENV-}esg-data-agent-scale-in-cpu-1"
  alarm_description = "This metric monitors cpu utilization < 25% for 1 datapoint in 1 minute"
  comparison_operator = "LessThanThreshold"
  evaluation_periods = "1"
  metric_name = "CPUUtilization"
  namespace = "AWS/ECS"
  period = "60"
  statistic = "Average"
  threshold = "25"
  datapoints_to_alarm = "1"

  dimensions = {
    ClusterName = "${module.common-prefix.env-}${local.cluster_name}"
    ServiceName = "${module.common-prefix.env-}${local.name}"
  }

  alarm_actions = [
    aws_appautoscaling_policy.esg-data-agent-scale-in-decreace-cpu-one.arn,
  ]
  tags = module.esg-tags.default_cloudwatch_metric_alarm_tags
}

resource aws_cloudwatch_metric_alarm esg-data-agent-add-capacity-cpu {
  alarm_name = "${module.common-prefix.ENV-}esg-data-agent-cpu-usage"
  alarm_description = "This metric monitors CPUUtilization >= 90"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods = "1"
  metric_name = "CPUUtilization"
  namespace = "AWS/ECS"
  period = "60"
  statistic = "Average"
  threshold = "90"

  dimensions = {
    ClusterName = "${module.common-prefix.env-}${local.cluster_name}"
    ServiceName = "${module.common-prefix.env-}${local.name}"
  }

  alarm_actions = [
    aws_appautoscaling_policy.esg-data-agent-scale-out-cpu.arn,
  ]
  tags = module.esg-tags.default_cloudwatch_metric_alarm_tags
}