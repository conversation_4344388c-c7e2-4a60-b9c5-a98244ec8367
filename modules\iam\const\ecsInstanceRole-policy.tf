data aws_caller_identity self {}

locals {
  iam-policies-ecs = [
    "arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceforEC2Role",
  ]
  iam-policies-ssm = [
    "arn:aws:iam::aws:policy/service-role/AmazonEC2RoleforSSM",
  ]
  iam-policies-ecs-with-ssm = distinct(concat(local.iam-policies-ecs, local.iam-policies-ssm))
  iam-policies-adclient = [
    "arn:aws:iam::${data.aws_caller_identity.self.account_id}:policy/mstar-chef-automation-kms-keys",
  ]
}

output iam-policies-ec2 {
  value = distinct(concat(local.iam-policies-adclient))
}

output iam-policies-ecs {
  value = distinct(concat(local.iam-policies-ecs, local.iam-policies-adclient))
}

output iam-policies-ecs-with-adclient-ssm {
  value = distinct(concat(local.iam-policies-ecs-with-ssm, local.iam-policies-adclient))
}
