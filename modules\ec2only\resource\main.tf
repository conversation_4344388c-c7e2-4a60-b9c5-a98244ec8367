module common-prefix {
  source = "../../prefix"
  mstar_env = var.mstar_env
  prefix = var.name
}

variable mstar_env {}

variable tid {
  default = "do"
}

variable name {}

variable "ssm_document_arn" {
  type = string
  default = null
  description = "The ARN of the SSM document to associate with the EC2 instance."
}

variable "efs_dns" {
  type = string
  default = ""
}

module "common-ec2-userdata-const" {
  source = "../../ec2/userdata/const"
  mstar_env = var.mstar_env
  ec2_type = module.common-prefix.env_prefix
  tid = var.tid
  efs_dns = var.efs_dns
}


locals {
  user_script_linux2 = (upper(var.mstar_env) == "PROD" || upper(var.mstar_env) == "UAT")? join("\n", tolist([
  module.common-ec2-userdata-const.yum_update,
  module.common-ec2-userdata-const.systemd_adclient,
  module.common-ec2-userdata-const.systemd_cloudbee,
  module.common-ec2-userdata-const.enable_splunk_agent,
  module.common-ec2-userdata-const.mstar_patch_agent_prod,
  module.common-ec2-userdata-const.install_telnet])):join("\n", tolist([
  module.common-ec2-userdata-const.yum_update,
  module.common-ec2-userdata-const.systemd_adclient,
  module.common-ec2-userdata-const.systemd_cloudbee,
  module.common-ec2-userdata-const.mstar_patch_agent,
  module.common-ec2-userdata-const.install_telnet]))
}
