resource "aws_lb_listener" "lb-pcs" {
  load_balancer_arn = aws_lb.lb-pcs.arn
  port = "80"
  protocol = "HTTP"
  default_action {
    target_group_arn = aws_lb_target_group.lb-group-pcs.arn
    type = "forward"
  }
}

resource "aws_lb_listener" "https_lb_listener" {
  load_balancer_arn = aws_lb.lb-pcs.arn
  port = "443"
  protocol = "HTTPS"
  ssl_policy = "ELBSecurityPolicy-2016-08"
  certificate_arn = aws_acm_certificate_validation.cert.certificate_arn
  default_action {
    target_group_arn = aws_lb_target_group.lb-group-pcs.arn
    type = "forward"
  }
}

resource "aws_acm_certificate_validation" "cert" {
  certificate_arn = aws_acm_certificate.cert.arn
  validation_record_fqdns = [
    aws_route53_record.cert_validation.fqdn,
  ]
}

resource "aws_route53_record" "cert_validation" {
  name = aws_acm_certificate.cert.domain_validation_options[0].resource_record_name
  type = aws_acm_certificate.cert.domain_validation_options[0].resource_record_type
  zone_id = data.aws_route53_zone.zone_domain.id
  records = [
    aws_acm_certificate.cert.domain_validation_options[0].resource_record_value,
  ]
  ttl = 60
}

data "aws_route53_zone" "zone_domain" {
  name = var.aws_zone_name_dataapi
  private_zone = false
}

resource "aws_acm_certificate" "cert" {
  domain_name = "data-agent-api${module.common-prefix._-env}.${replace(var.aws_zone_name_dataapi, "/[.]$/", "")}"
  validation_method = "DNS"
  tags = module.esg-tags.default_certificate_tags
  lifecycle {
    create_before_destroy = true
  }
}