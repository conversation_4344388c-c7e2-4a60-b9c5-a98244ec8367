module "common-iam-const" {
  source = "../../modules/iam/const"
}

module "common-caller" {
  source = "../../modules/caller"
}

module "common-iam" {
  source = "../../modules/iam/resource"
  mstar_env = var.mstar_env
  name = "ondemand-jobs-manager-s3-trigger-role"
  use_role_name_prefix = false
  service_prefix = ""
  assume_role_policy = module.common-iam-const.assume-role-policy-lambda
  policies = distinct(concat(module.common-iam-const.iam-policies-lambda-vpc, module.common-iam-const.iam-policies-lambda-basic, module.common-iam-const.iam-policies-lambda-role, module.common-iam-const.iam-policies-rds))
  policy = [
    module.common-iam-policies.template_content,
  ]
  iam_role_tags = module.esg-tags.default_iam_role_tags
}

module "common-iam-policies" {
  source = "../../modules/iam/policies"
  templates = [
    "ssm",
    "s3",
    "kms",
  ]
  vars = {
    parameter_kms_key_arn = module.common-kms.ref-pas-data-result-key-arn
    parameter_keys = jsonencode(module.common-kms.para-ondemand-jobs-rds-password)
    input_buckets = jsonencode(list(local.s3_do_pas_results_arn))
    input_bucket_files = jsonencode(list("${local.s3_do_pas_results_arn}/*"))
    kms_key_arns = jsonencode(list(module.common-kms.ref-pas-data-result-key-arn))
  }
}

module "common-kms" {
  source = "../../modules/kms"
  mstar_env = var.mstar_env
}

