module common-prefix {
  source = "../../../../prefix"
  mstar_env = var.mstar_env
}

variable mstar_env {
}

resource aws_s3_bucket_public_access_block no_replication {
  count = local.enable_bucket && !local.enable_replication? 1:0
  bucket = aws_s3_bucket.no_replication[0].id
  block_public_acls = true
  block_public_policy = true
  ignore_public_acls = true
  restrict_public_buckets = true
  depends_on = [
    aws_s3_bucket.no_replication
  ]
}

resource aws_s3_bucket no_replication {
  count = local.enable_bucket && !local.enable_replication? 1:0
  bucket = "${module.common-prefix.env-}${local.name}"
  tags = var.s3_bucket_tags
  versioning {
    enabled = true
  }
  dynamic lifecycle_rule {
    for_each = local.lifecycle_rules
    iterator = _
    content {
      prefix = _.value.prefix
      enabled = _.value.enabled
      expiration {
        days = _.value.expiration.days
        expired_object_delete_marker = _.value.expiration.expired_object_delete_marker
      }
      noncurrent_version_expiration {
        days = _.value.noncurrent_version_expiration.default_nonconcurrent_version_expiration
      }
    }
  }
  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        kms_master_key_id = data.aws_kms_alias.key.target_key_arn
        sse_algorithm = "aws:kms"
      }
    }
  }
}

resource aws_s3_bucket_public_access_block bucket {
  count = local.enable_bucket && local.enable_replication? 1:0
  bucket = aws_s3_bucket.bucket[0].id
  block_public_acls = true
  block_public_policy = true
  ignore_public_acls = true
  restrict_public_buckets = true
  depends_on = [
    aws_s3_bucket.bucket
  ]
}

resource aws_s3_bucket bucket {
  count = local.enable_bucket && local.enable_replication? 1:0
  bucket = "${module.common-prefix.env-}${local.name}"
  tags = var.s3_bucket_tags
  versioning {
    enabled = true
  }
  dynamic lifecycle_rule {
    for_each = local.lifecycle_rules
    iterator = _
    content {
      prefix = _.value.prefix
      enabled = _.value.enabled
      expiration {
        days = _.value.expiration.days
        expired_object_delete_marker = _.value.expiration.expired_object_delete_marker
      }
      noncurrent_version_expiration {
        days = _.value.noncurrent_version_expiration.default_nonconcurrent_version_expiration
      }
    }
  }
  replication_configuration {
    role = local.replication_role_arn
    rules {
      id = local.replica_arn
      source_selection_criteria {
        sse_kms_encrypted_objects {
          enabled = true
        }
      }
      destination {
        bucket = local.replica_arn
        replica_kms_key_id = local.replication_key_arn
      }
      prefix = ""
      status = local.enable_replication? "Enabled":"Disabled"
    }
  }
  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        kms_master_key_id = data.aws_kms_alias.key.target_key_arn
        sse_algorithm = "aws:kms"
      }
    }
  }
}

variable key_name {
}

variable replication_key_name {
}

data "aws_kms_alias" key {
  name = var.key_name
}

data "aws_kms_alias" replication_key {
  count = local.enable_replication? 1:0
  name = var.replication_key_name
  provider = aws.replication
}

data "aws_kms_alias" replication_key_dr {
  count = local.enable_replication? 1:0
  name = var.replication_key_name
  provider = aws.s3default
}

module "common-s3-lifecycle_rule" {
  source = "../../../lifecycle_rule"
}

locals {
  lifecycle_rules = distinct(concat(list(module.common-s3-lifecycle_rule.rule), var.lifecycle_rules))
}

variable lifecycle_rules {
  type = list(object({
    prefix = string
    enabled = bool
    expiration = object({
      days = number
      expired_object_delete_marker = bool
    })
    noncurrent_version_expiration = object({
      default_nonconcurrent_version_expiration = number
    })
  }))
  default = []
}

variable name {
}

variable name_prefix {
  default = "mstar-dwm-"
}

variable qa_catchall {
  default = true
}

variable s3_bucket_tags {
  type = map(string)
}

locals {
  catchall_enabled = var.qa_catchall && (module.common-prefix.ENV == "QA" || module.common-prefix.ENV == "DEV")
  enable_bucket = !local.catchall_enabled
  resource = local.enable_replication? aws_s3_bucket.bucket: aws_s3_bucket.no_replication
  resource_arn = local.resource[0].arn
  resource_id = local.resource[0].id
  key_arn = data.aws_kms_alias.key.target_key_arn
}

output arn {
  value = local.resource_arn
}

output key_arn {
  value = local.key_arn
}

output id {
  value = local.resource_id
}

locals {
  name = "${var.name_prefix}${var.name}"
}

output enable_bucket {
  value = local.enable_bucket
}
