#!/usr/bin/env bash

if [ "${deploy_env}" = "PROD" ]; then
  source ./assume_role.sh ${PROD_ACCOUNT} ${PROD_ROLE}
  ECR_URL="${PROD_ACCOUNT}.dkr.ecr.${AWS_REGION}.amazonaws.com"
elif [ "${deploy_env}" = "non-PROD" ]; then
  source ./assume_role.sh ${NONPROD_ACCOUNT} ${NONPROD_ROLE}
  ECR_URL="${NONPROD_ACCOUNT}.dkr.ecr.${AWS_REGION}.amazonaws.com"
fi

set +x
$(aws ecr get-login --region ${AWS_REGION} | sed 's/-e none//')

[ ! -z "${ARTIFACT_GROUP}" ] && [ ! -z "${ARTIFACT_NAME}" ] && (
  repository="${ARTIFACT_GROUP}/${ARTIFACT_NAME}"
  REMOTE_TAG="${ECR_URL}/${repository}:${NEXUS_ARTIFACT_VERSION}"
  echo "fetch ${ARTIFACT_NAME} from ${REMOTE_TAG}"
  docker rmi "${REMOTE_TAG}"
  docker pull "${REMOTE_TAG}"
) || echo "cannot fetch ${ARTIFACT_GROUP}/${ARTIFACT_NAME}"