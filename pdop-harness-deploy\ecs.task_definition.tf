module "common-ecs-task" {
  source = "../modules/ecs/resource/service_awsvpc/task"
  mstar_env = var.mstar_env
  service = local.name
  cluster = local.cluster_name
  role = module.common-iam.arn
  repository = local.repo_name
  ecs_tag_version = var.ecs_tag_version
  cpu = 4096
  memory = 16384
  ecs_task_tags = module.pdop-tags.default_ecs_task_tags
  java_options = module.common-java.java-options
  portMappings = [
    {
      containerPort = 80
      hostPort = 80
      protocol = "tcp"
    }
  ]
  ulimits = [
    {
      name = "nofile"
      softLimit = 1048576
      hardLimit = 1048576
    }
  ]
}
