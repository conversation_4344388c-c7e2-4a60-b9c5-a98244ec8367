#!/usr/bin/env bash
# hostname=%replace%
# ipaddress="`/sbin/ifconfig|grep inet|grep "10."|awk -F ' ' '{printf $2}'`"
# echo $ipaddress > "$hostname"
# echo "preserve_hostname = true" >> /etc/cloud/cloud.cfg
# hostnamectl set-hostname "$hostname.morningstar.com"

if [[ "${base-env}" = "prod" ]] || [[ "${base-env}" = "uat" ]]; then
aws s3 cp $hostname s3://dcs-deploy-prod-tempfolder/${ec2-type}_hostlist/
else
aws s3 cp $hostname s3://dcs-deploy-tempfolder/${ec2-type}_hostlist/
fi
