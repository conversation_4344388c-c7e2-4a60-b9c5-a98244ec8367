module common-prefix {
  source = "../../../prefix"
  mstar_env = var.mstar_env
  prefix = var.name
}

variable mstar_env {}

variable name {}

data aws_ecs_cluster cluster {
  cluster_name = "${module.common-prefix.env-}${var.cluster}"
}

variable cluster {}

module common-ec2-userdata-const {
  source = "../const"
  mstar_env = var.mstar_env
}

data template_file ecs_init {
  template = module.common-ec2-userdata-const.ecs_init
  vars = {
    ecs_cluster = data.aws_ecs_cluster.cluster.cluster_name
    ecs_instance_attributes = jsonencode(local.instance_attributes)
  }
}

variable ecs_extra_user_data {
  default = ""
}

locals {
  instance_attributes = {
    service = var.name
  }
}

output user_data {
  value = join("\n", list(data.template_file.ecs_init.rendered, var.ecs_extra_user_data))
}
