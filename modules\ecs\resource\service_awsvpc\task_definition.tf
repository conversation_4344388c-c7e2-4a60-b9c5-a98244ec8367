module common-ecs-service-task {
  source = "./task"
  mstar_env = var.mstar_env
  cluster = var.cluster
  service = var.service
  role = var.role
  ecs_tag_version = var.ecs_tag_version
  cpu = var.cpu
  memory = var.memory
  java_options = var.java_options
  environment = var.environment
  repository = var.repository
  use_ec2 = var.use_ec2
  ecs_task_tags = var.ecs_task_tags
  portMappings = var.portMappings
}

variable repository {}

variable role {}

variable cpu {}

variable memory {}

variable ecs_tag_version {}

variable java_options {
  type = list(string)
}

variable environment {
  type = map(string)
  default = {}
}

variable ecs_task_tags {
  type = map(string)
  default = {}
}

variable portMappings {
  type = list(any)
  default = []
}