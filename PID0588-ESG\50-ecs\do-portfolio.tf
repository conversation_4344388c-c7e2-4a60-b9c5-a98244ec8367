resource aws_ecs_cluster do-portfolio {
  name = "${module.common-prefix.env-}do-portfolio"
  tags = module.esg-tags.default_ecs_cluster_tags
}

module common-ecs-logs-do-portfolio {
  source = "../../modules/ecs/logs"
  mstar_env = var.mstar_env
  name = "do-portfolio"
  service_name = ""
}

resource aws_cloudwatch_log_group do-portfolio {
  name = module.common-ecs-logs-do-portfolio.log_group
  tags = module.esg-tags.default_log_group_tags
  retention_in_days = var.do-portfolio-log_retention
}

variable do-portfolio-log_retention {
  default = 7
}