module common-prefix {
  source = "../../prefix"
  mstar_env = var.mstar_env
}

variable mstar_env {
}

locals {
  isSTG = module.common-prefix.ENV == "STG" || module.common-prefix.ENV == "STG-DR"
}

output client-account-cdp-delta {
  value = local.isSTG? "clientaccount-custom-datapoint-delta" : "client-account-cdp-delta"
}

output client-account-custom-datapoint {
  value = local.isSTG? "clientaccount-custom-datapoint" : "client-account-custom-datapoint"
}

output client-account-custom-dri-result {
  value = local.isSTG? "clientaccountdri" : "client-account-custom-dri-result"
}

output client-account-profile-master {
  value = local.isSTG? "account-profile-master" : "client-account-profile-master"
}

output client-accountdailymarketvalue-result {
  value = local.isSTG? "accountdailymarketvalue" : "client-accountdailymarketvalue-result"
}

output client-accountdri-result {
  value = local.isSTG? "accountdri" : "client-accountdri-result"
}

output client-accountholding {
  value = local.isSTG? "accountholdings" : "client-accountholding"
}

output client-accountholding-delta {
  value = local.isSTG? "accountholding-delta" : "client-accountholding-delta"
}

output client-accountinferholdingweight-result {
  value = local.isSTG? "accountinferholdingweight" : "client-accountinferholdingweight-result"
}

output client-acct-breakdown-statistics-result {
  value = local.isSTG? "breakdown-statistics-result" : "client-acct-breakdown-statistics-result"
}

output client-security-cdp-delta {
  value = local.isSTG? "clientsecurity-custom-datapoint-delta" : "client-security-cdp-delta"
}

output client-security-custom-datapoint {
  value = local.isSTG? "clientsecurity-custom-datapoint" : "client-security-custom-datapoint"
}

output client-security-dri-result {
  value = local.isSTG? "clientsecuritydri" : "client-security-dri-result"
}

output client-security-master {
  value = local.isSTG? "clientsecuritymaster" : "client-security-master"
}

output activeshare-result {
  value = local.isSTG? "portfolio-activeshare-result" : "mstar-dwm-activeshare-result"
}

output mstarsecuritydri {
  value = local.isSTG? "timeseries-dri" : "mstar-dwm-mstarsecuritydri"
}

