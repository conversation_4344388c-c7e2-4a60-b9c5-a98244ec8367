locals {
  nonprod_pas_holdings_principals = [
    module.common-caller-const.datasvc_client_portfolio_esg_pas_ondemand_stg,
    module.common-caller-const.datasvc_client_portfolio_esg_pas_ondemand_qa,
    module.common-caller-const.datasvc_client_portfolio_esg_pas_ondemand_batch_stg,
    module.common-caller-const.datasvc_client_portfolio_fixedincome_pas_ondemand_stg,
    module.common-caller-const.datasvc_client_portfolio_fixedincome_pas_ondemand_batch_stg,
    module.common-caller-const.datasvc_client_portfolio_carbon_pas_ondemand_stg,
    module.common-caller-const.datasvc_client_portfolio_carbon_pas_ondemand_batch_stg,
    module.common-caller-const.datasvc_client_portfolio_fof_api_holding_stg
  ]
  prod_pas_holdings_principals = [
    module.common-caller-const.datasvc_client_portfolio_esg_pas_ondemand_prod,
    module.common-caller-const.datasvc_client_portfolio_esg_pas_ondemand_batch_prod,
    module.common-caller-const.datasvc_client_portfolio_carbon_pas_ondemand_prod,
    module.common-caller-const.datasvc_client_portfolio_carbon_pas_ondemand_batch_prod,
    module.common-caller-const.datasvc_client_portfolio_fixedincome_pas_ondemand_prod,
    module.common-caller-const.datasvc_client_portfolio_fixedincome_pas_ondemand_batch_prod,
    module.common-caller-const.datasvc_client_portfolio_fof_api_holding_prod,
    module.common-caller-const.do_client_portfolio_s3_decrypt_prod
  ]
  dr_pas_holdings_principals = [
    module.common-caller-const.datasvc_client_portfolio_esg_pas_ondemand_dr,
    module.common-caller-const.datasvc_client_portfolio_esg_pas_ondemand_batch_dr,
    module.common-caller-const.datasvc_client_portfolio_carbon_pas_ondemand_dr,
    module.common-caller-const.datasvc_client_portfolio_carbon_pas_ondemand_batch_dr,
    module.common-caller-const.datasvc_client_portfolio_fixedincome_pas_ondemand_dr,
    module.common-caller-const.datasvc_client_portfolio_fixedincome_pas_ondemand_batch_dr,
    module.common-caller-const.datasvc_client_portfolio_fof_api_holding_dr
  ]

  pas-holdings-principals = module.common-prefix.isPROD? local.prod_dr_pas_holdings_principals : local.nonprod_pas_holdings_principals
  prod_dr_pas_holdings_principals = concat(local.dr_pas_holdings_principals , local.prod_pas_holdings_principals)
}

module common-s3-mstar-pas-holdings {
  source = "../../modules/s3"
  name = "do-mstar-pas-holdings"
  name_prefix = ""
  mstar_env = var.mstar_env
}

module common-s3-bucket_files-policies-mstar-pas-holdings {
  source = "../../modules/s3/bucket_files_v2"
  s3-all-buckets = [
    module.common-s3-mstar-pas-holdings.arn_prefix,
  ]
}

module common-iam-s3-policies-mstar-pas-holdings {
  source = "../../modules/iam/s3-policies"
  templates = [
    "s3"
  ]
  vars = {
    aws_principals = jsonencode(distinct(local.pas-holdings-principals))
    s3_bucket = module.common-s3-bucket_files-policies-mstar-pas-holdings.buckets-json
    s3_bucket_files = module.common-s3-bucket_files-policies-mstar-pas-holdings.bucket_files-json
  }
}

variable "enable_s3_bucket_policy-mstar-pas-holdings" {
  default = false
}

resource "aws_s3_bucket_policy" "policy-mstar-pas-holdings" {
  count = var.enable_s3_bucket_policy-mstar-pas-holdings && length(local.pas-holdings-principals)>0 ?1:0
  bucket = module.common-s3-mstar-pas-holdings.id
  policy = module.common-iam-s3-policies-mstar-pas-holdings.template_content
}