module "dcs-ec2-ads" {
  source = "../../modules/ec2only/resource"
  security_groups = module.common-vpc-sg.default_ec2_security_group
  mstar_env = var.mstar_env
  ec2_tags = module.dcs-tags.default_e2only_tags
  name = local.name
  subnet_id = data.aws_subnet.selected.id
  availability_zone = var.ec2_aws_availability_zone
  aws_instance_type = var.aws_instance_type
  ec2_count = var.ec2_instance_count
  name-prefix = module.common-prefix.env_prefix
  user_data = local.user_data
  image_id = var.image_id
  ssm_document_arn = "arn:aws:ssm:us-east-1:903581193685:document/mstar-base-amazon2023"
}

locals {
  name = "dcs_worker_planning"
  user_data =  join("\n", list(
  module.common-ec2-userdata-const.install_dcs_worker_planning))
}

variable aws_instance_type {
  default = "m5.2xlarge"
}

variable ec2_instance_count {
  default = 1
}

variable additional_volumes {
  default = [
    {
      device_name = "/dev/xvda"
      delete_on_termination = true
      encrypted = false
      volume_size = 60
      volume_type = "gp2"
    }
  ]
}

variable image_id {
  default = "ami-0b10d2a25d66f56b2"
}

module "common-ec2-userdata-const" {
  source = "../../modules/ec2/userdata/const"
  mstar_env = var.mstar_env
}

variable "aws_zone_name_dataapi" {}

module "dcs_planning_route53" {
  source = "../../modules/route53"
  name_ips = module.dcs-ec2-ads.private_name_ips
  aws_zone_name = var.aws_zone_name_dataapi
}
