module "common-prefix" {
  source = "../../modules/prefix"
  mstar_env = var.mstar_env
}

module "common-vpc-sg" {
  source = "../../modules/vpc/sg"
  aws_security_groups = var.aws_security_groups
  default_lb_security_group = var.default_lb_security_group
}

module "common-vpc" {
  source = "../../modules/vpc"
  aws_vpc = var.aws_vpc
  aws_availability_zone = var.aws_availability_zone
}

module "common-kms" {
  source = "../../modules/kms"
  mstar_env = var.mstar_env
}

variable aws_vpc {}

variable mstar_env {}

variable aws_security_groups {}

variable default_lb_security_group {}

variable aws_availability_zone {}