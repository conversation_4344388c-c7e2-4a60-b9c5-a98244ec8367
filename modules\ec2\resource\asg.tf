resource aws_autoscaling_group asg_with_launch_template {
  count = var.use_asg_name_prefix && var.enable_scaling && var.enable_asg? 1:0
  name_prefix = module.common-prefix.env-prefix
  launch_template {
    id = aws_launch_template.lt.id
    version = aws_launch_template.lt.latest_version
  }
  max_size = var.max_size
  min_size = var.min_size
  desired_capacity = var.initial_capacity
  availability_zones = var.availability_zones
  vpc_zone_identifier = var.subnet_ids
  tags = var.asg_tags
  force_delete = true
  lifecycle {
    create_before_destroy = true
    ignore_changes = [
      desired_capacity,
    ]
  }
}

resource aws_autoscaling_group asg_with_launch_template-no-scaling {
  count = var.use_asg_name_prefix && !var.enable_scaling && var.enable_asg? 1:0
  name_prefix = module.common-prefix.env-prefix
  launch_template {
    id = aws_launch_template.lt.id
    version = aws_launch_template.lt.latest_version
  }
  max_size = var.max_size
  min_size = var.min_size
  desired_capacity = var.initial_capacity
  availability_zones = var.availability_zones
  vpc_zone_identifier = var.subnet_ids
  tags = var.asg_tags
  force_delete = true
  lifecycle {
    create_before_destroy = true
  }
}

variable asg_tags {
  type = list(any)
}

variable "cloudwatch_metric_alarm_tags" {
  type = map(string)
  default = {}
}

variable max_size {
  default = 0
}

variable min_size {
  default = 0
}

variable initial_capacity {
  default = 0
}

variable use_asg_name_prefix {
  default = true
}

locals {
  asg = var.use_asg_name_prefix? (!var.enable_scaling? aws_autoscaling_group.asg_with_launch_template-no-scaling: aws_autoscaling_group.asg_with_launch_template): (!var.enable_scaling? aws_autoscaling_group.asg_with_exact_name_with_launch_template-no-scaling: aws_autoscaling_group.asg_with_exact_name_with_launch_template)
  asg_name = var.enable_asg? local.asg[0].name:""
  asg_arn = var.enable_asg? local.asg[0].arn:""
}

output asg {
  value = local.asg_name
}

output asg_arn {
  value = local.asg_arn
}

variable enable_scaling {
  default = true
}

variable enable_asg {
  default = true
}
