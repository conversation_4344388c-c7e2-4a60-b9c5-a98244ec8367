resource aws_lb_target_group lb-group-portfolio-setting {
  name = "${module.common-prefix.env-}portofolio-setting-group"
  port = 80
  protocol = "HTTP"
  target_type = "ip"
  vpc_id = module.common-vpc.default_vpc

  health_check {
    interval = 300
    path = "/version"
    port = 80
    protocol = "HTTP"
    timeout = 5
    healthy_threshold = 5
    unhealthy_threshold = 2
    matcher = "200"
  }
  tags = module.pdop-tags.default_lb_target_group_tags
}
