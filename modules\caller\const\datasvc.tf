variable datasvc_nonprod {
  default = 187914334366
}

variable datasvc_prod {
  default = "062740692535"
}

variable do_s3_decrypt_prod {
  default = "012811347870"
}

output datasvc_nonprod_EMR {
  value = "arn:aws:iam::${var.datasvc_nonprod}:role/EMR_EC2_DefaultRole"
}

output datasvc_prod_operator {
  value = "arn:aws:iam::${var.datasvc_prod}:role/mstar-datasvc-prod-operator"
}

output datasvc_nonprod_operator {
  value = "arn:aws:iam::${var.datasvc_nonprod}:role/mstar-datasvc-non-prod-operator"
}

output datasvc_fixedIncomeOperator {
  value = "arn:aws:iam::${var.datasvc_nonprod}:role/tools/fixedIncomeDataOperator"
}

output datasvc_fixedIncomeSageMaker {
  value = "arn:aws:iam::${var.datasvc_nonprod}:role/tools/fixedIncomeDataSageMaker"
}

output datasvc_fixedIncomeAthena {
  value = "arn:aws:iam::${var.datasvc_nonprod}:role/tools/fixedIncomeDataAthena"
}

output datasvc_fixedIncomeEMR {
  value = "arn:aws:iam::${var.datasvc_nonprod}:role/tools/fixedIncomeDataEMR"
}

output datasvc_portfolio_ {
  value = "arn:aws:iam::${var.datasvc_nonprod}:role/portfolio-service/"
}

output datasvc_prod_fixedincome_idservice_search_loader {
  value = "arn:aws:iam::${var.datasvc_prod}:role/fixedincome-service/ecs-fixedincome-search-loader"
}

output datasvc_client_portfolio_esg_pas_ondemand_prod {
  value = "arn:aws:iam::${var.datasvc_prod}:role/portfolio-service/esg-pas-ondemand-role"
}

output datasvc_client_portfolio_esg_pas_ondemand_dr {
  value = "arn:aws:iam::${var.datasvc_prod}:role/portfolio-service/dr-esg-pas-ondemand-role"
}

output datasvc_client_portfolio_esg_pas_ondemand_batch_prod {
  value = "arn:aws:iam::${var.datasvc_prod}:role/portfolio-service/esg-pas-ondemand-batch-role"
}

output datasvc_client_portfolio_esg_pas_ondemand_batch_dr {
  value = "arn:aws:iam::${var.datasvc_prod}:role/portfolio-service/dr-esg-pas-ondemand-batch-role"
}

output datasvc_client_portfolio_carbon_pas_ondemand_prod {
  value = "arn:aws:iam::${var.datasvc_prod}:role/portfolio-service/esg-carbon-pas-ondemand-role"
}

output datasvc_client_portfolio_carbon_pas_ondemand_dr {
  value = "arn:aws:iam::${var.datasvc_prod}:role/portfolio-service/dr-esg-carbon-pas-ondemand-role"
}

output datasvc_client_portfolio_carbon_pas_ondemand_batch_prod {
  value = "arn:aws:iam::${var.datasvc_prod}:role/portfolio-service/esg-carbon-pas-ondemand-batch-role"
}

output datasvc_client_portfolio_fof_api_holding_prod {
  value = "arn:aws:iam::${var.datasvc_prod}:role/portfolio-service/ecs-dp-fof-api"
}

output datasvc_client_portfolio_carbon_pas_ondemand_batch_dr {
  value = "arn:aws:iam::${var.datasvc_prod}:role/portfolio-service/dr-esg-carbon-pas-ondemand-batch-role"
}

output datasvc_client_portfolio_fixedincome_pas_ondemand_prod {
  value = "arn:aws:iam::${var.datasvc_prod}:role/portfolio-service/fixedincome-pas-ondemand-role"
}

output datasvc_client_portfolio_fixedincome_pas_ondemand_dr {
  value = "arn:aws:iam::${var.datasvc_prod}:role/portfolio-service/dr-fixedincome-pas-ondemand-role"
}

output datasvc_client_portfolio_fixedincome_pas_ondemand_batch_prod {
  value = "arn:aws:iam::${var.datasvc_prod}:role/portfolio-service/fixedincome-pas-ondemand-batch-role"
}

output datasvc_client_portfolio_fixedincome_pas_ondemand_batch_dr {
  value = "arn:aws:iam::${var.datasvc_prod}:role/portfolio-service/dr-fixedincome-pas-ondemand-batch-role"
}

output datasvc_client_portfolio_fof_api_holding_dr {
  value = "arn:aws:iam::${var.datasvc_prod}:role/portfolio-service/ecs-dp-fof-api"
}

output datasvc_client_portfolio_esg_pas_ondemand_qa {
  value = "arn:aws:iam::${var.datasvc_nonprod}:role/portfolio-service/qa-esg-pas-ondemand-role"
}

output datasvc_client_portfolio_esg_pas_ondemand_batch_stg {
  value = "arn:aws:iam::${var.datasvc_nonprod}:role/portfolio-service/stg-esg-pas-ondemand-batch-role"
}

output datasvc_client_portfolio_esg_pas_ondemand_stg {
  value = "arn:aws:iam::${var.datasvc_nonprod}:role/portfolio-service/stg-esg-pas-ondemand-role"
}

output datasvc_client_portfolio_fixedincome_pas_ondemand_stg {
  value = "arn:aws:iam::${var.datasvc_nonprod}:role/portfolio-service/stg-fixedincome-pas-ondemand-role"
}

output datasvc_client_portfolio_fixedincome_pas_ondemand_batch_stg {
  value = "arn:aws:iam::${var.datasvc_nonprod}:role/portfolio-service/stg-fixedincome-pas-ondemand-batch-role"
}

output datasvc_client_portfolio_carbon_pas_ondemand_stg {
  value = "arn:aws:iam::${var.datasvc_nonprod}:role/portfolio-service/stg-esg-carbon-pas-ondemand-role"
}

output datasvc_client_portfolio_carbon_pas_ondemand_batch_stg {
  value = "arn:aws:iam::${var.datasvc_nonprod}:role/portfolio-service/stg-esg-carbon-pas-ondemand-batch-role"
}

output datasvc_client_portfolio_fof_api_holding_stg {
  value = "arn:aws:iam::${var.datasvc_nonprod}:role/portfolio-service/ecs-dp-fof-api"
}

output do_client_portfolio_s3_decrypt_prod {
  value = "arn:aws:iam::${var.do_s3_decrypt_prod}:role/aws-reserved/sso.amazonaws.com/AWSReservedSSO_mstar-do-prod-s3-decrypt_0cf6372738430eef"
}