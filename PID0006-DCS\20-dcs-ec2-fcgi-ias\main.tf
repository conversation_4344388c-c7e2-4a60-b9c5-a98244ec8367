module "common-prefix" {
  source = "../../modules/prefix"
  mstar_env = var.mstar_env
  prefix = local.name
}

module "common-vpc-sg" {
  source = "../../modules/vpc/sg"
  aws_security_groups = var.aws_security_groups
  default_ec2_security_group = var.default_ec2_security_group
}

variable "aws_security_groups" {
  type = list(string)
}

variable "default_ec2_security_group" {
  type = list(string)
  default = []
}

variable "mstar_env" {
  default = "us-east-1"
}

variable "aws_vpc" {
  default = "vpc-0a6fb75fd2526c3ae"
}

variable "ec2_aws_availability_zone" {
  type = string
}

data aws_vpc ec2 {
  id = var.aws_vpc_ec2
}

variable aws_vpc_ec2 {
}

variable key_name {
  default = "abacus_linux_nonprod"
}

variable special_name {
  default = "us-east-1c-private"
}

data "aws_subnet" "selected" {
  vpc_id = data.aws_vpc.ec2.id
  filter {
    name   = "tag:Name"
    values = [
      "${var.special_name}",
    ]
  }
}

output special_private_subnet_ids {
  value = data.aws_subnet.selected[*].id
}

