#!/usr/bin/env sh

set +x

export ecs_tag_version=[version]
export TF_MODULES="[modules]"
export TF_ACTION=[action]
export MSTAR_ENV=[env]
export TF_PROFILE=[profile]
export TERRAFORM_IMAGE="hashicorp/terraform:0.12.31"
export NONPROD_ACCOUNT="************"
export NONPROD_ROLE="DO-ENGR-Deploy"
export PROD_ACCOUNT="************"
export PROD_ROLE="DO-ENGR-Deploy"

echo "*** docker version"
docker -v
pwd

path="$PWD"

AWS_CREDENTIALS=`mktemp`

if [ "x${MSTAR_ENV^^}" = "xPROD" ] || [ "x${MSTAR_ENV^^}" = "xDR" ] || [ "x${MSTAR_ENV^^}" = "xUAT" ]; then
   source ./assume_role.sh ${PROD_ACCOUNT} ${PROD_ROLE} ${AWS_CREDENTIALS}
else
   source ./assume_role.sh ${NONPROD_ACCOUNT} ${NONPROD_ROLE} ${AWS_CREDENTIALS}
fi

docker run \
  --rm \
  -v "$(pwd)":/deploy:ro \
  -v ${AWS_CREDENTIALS}:/aws_credentials:ro \
  --entrypoint sh \
  --tmpfs /tmp:exec \
  -e TF_VAR_ecs_tag_version="${ecs_tag_version}" \
  -e TF_MODULES="${TF_MODULES}" \
  -e TF_PROFILE="${TF_PROFILE}" \
  -e MSTAR_ENV="${MSTAR_ENV}" \
  ${TERRAFORM_IMAGE} \
  -c "cp -R /deploy /tmp && \
    cd /tmp/deploy/[TF_BASE] && \
     ( source /aws_credentials && sh ../contrib/terraform-wrapper-0.12.sh ${TF_ACTION} ${TF_MODULES})"
