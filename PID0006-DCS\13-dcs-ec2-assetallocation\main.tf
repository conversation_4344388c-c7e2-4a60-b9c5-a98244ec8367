module "common-prefix" {
  source = "../../modules/prefix"
  mstar_env = var.mstar_env
  prefix = local.name
}

module "common-vpc-sg" {
  source = "../../modules/vpc/sg"
  aws_security_groups = var.aws_security_groups
  default_ec2_security_group = var.default_outposts_security_group
}

variable "aws_security_groups" {
  type = list(string)
}

variable "default_outposts_security_group" {
  type = list(string)
  default = []
}

variable "mstar_env" {
}

# variable "aws_vpc" {
# }

variable "ec2_aws_availability_zone" {
  type = string
}

data aws_vpc selected {
  id = var.aws_vpc_selected
}

variable aws_vpc_selected {
}

variable special_name {
  default = "us-east-1d-private"
}

data "aws_subnet" "selected" {
  vpc_id = data.aws_vpc.selected.id
  filter {
    name   = "tag:Name"
    values = [
      "${var.special_name}",
    ]
  }
}

output special_private_subnet_ids {
  value = data.aws_subnet.selected[*].id
}

