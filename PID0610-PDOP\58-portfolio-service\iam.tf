module "common-iam" {
  source = "../../modules/iam/resource"
  mstar_env = var.mstar_env
  name = "${local.name}-role"
  service_prefix = ""
  use_role_name_prefix = false
  policies = distinct(concat(module.common-iam-const.iam-policies-ecsTask, module.common-iam-const.iam-policies-elasticache-readonly))
  policy = list(module.common-iam-policies.template_content)
  iam_role_tags = module.pdop-tags.default_iam_role_tags
}

module "common-s3-do-mstar-pas-holdings" {
  source = "../../modules/caller/const"
  prefix = module.common-prefix.env == "qa" ? "stg-" : (module.common-prefix.env == "uat" || module.common-prefix.env == "prod" ? "" : module.common-prefix.env-)
  name = "do-mstar-pas-holdings"
}

module "common-s3-datalake-custom-model" {
  source = "../../modules/caller/const"
  prefix = (module.common-prefix.env == "qa" || module.common-prefix.env == "stg") ? "datalake-stage-" : "datalake-prod-"
  name = "drop-00-us-east-1"
}

module "common-s3-direct-custom-model" {
  source = "../../modules/caller/const"
  suffix = (module.common-prefix.env == "qa" || module.common-prefix.env == "stg") ? "-non-prod" : "-prod"
  name = "direct-custom-model"
}

module "common-s3-bucket_files-output" {
  source = "../../modules/s3/bucket_files_v2"
  s3-all-buckets = [
    module.common-s3-do-mstar-pas-holdings.arn_prefix,
    module.common-s3-datalake-custom-model.arn_prefix,
    module.common-s3-direct-custom-model.arn_suffix
  ]
}

module "common-iam-policies" {
  source = "../../modules/iam/policies"
  templates = [
    "kms",
    "s3crud",
    "ssm",
  ]
  vars = {
    kms_key_arns = jsonencode(list(module.common-kms.ref-pas-data-result-key-arn))
    output_buckets = module.common-s3-bucket_files-output.buckets-json
    output_bucket_files = module.common-s3-bucket_files-output.bucket_files-json
    parameter_kms_key_arn = module.common-kms.ref-pas-data-result-key-arn
    parameter_keys = jsonencode(module.common-kms.para-api-key-value)
  }
}