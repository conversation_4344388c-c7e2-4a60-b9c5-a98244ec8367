resource aws_api_gateway_rest_api data_agent_esg {
  name  = "${module.common-prefix.env-}data-agent-esg-api"
  policy = module.common-iam-policies-apigateway.template_content
  tags = module.esg-tags.default_api_gateway_tags
  description = "This is the API for ${module.common-prefix.env-}data-agent-esg"
  endpoint_configuration {
    types = ["PRIVATE"]
  }
}

#for method trigger job calc -X POST
resource aws_api_gateway_resource trigger_source {
  path_part   = "trigger"
  parent_id   = aws_api_gateway_rest_api.data_agent_esg.root_resource_id
  rest_api_id = aws_api_gateway_rest_api.data_agent_esg.id
}

resource aws_api_gateway_method method_post_trigger {
  rest_api_id   = aws_api_gateway_rest_api.data_agent_esg.id
  resource_id   = aws_api_gateway_resource.trigger_source.id
  http_method   = "POST"
  authorization = "NONE"
}

resource aws_api_gateway_integration integration_trigger_source {
  rest_api_id             = aws_api_gateway_rest_api.data_agent_esg.id
  resource_id             = aws_api_gateway_resource.trigger_source.id
  http_method             = aws_api_gateway_method.method_post_trigger.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = aws_lambda_function.lambda.invoke_arn
}

resource aws_lambda_permission apigw_lambda_trigger {
  statement_id  = "AllowExecutionFromAPIGatewayTrigger"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.lambda.function_name
  principal     = "apigateway.amazonaws.com"

  source_arn = "arn:aws:execute-api:${local.region}:${module.common-caller.account_id}:${aws_api_gateway_rest_api.data_agent_esg.id}/*/${aws_api_gateway_method.method_post_trigger.http_method}${aws_api_gateway_resource.trigger_source.path}"
}

#for method post /holdings -X POST
resource "aws_api_gateway_resource" holdings_source {
  path_part = "holdings"
  parent_id   = aws_api_gateway_rest_api.data_agent_esg.root_resource_id
  rest_api_id = aws_api_gateway_rest_api.data_agent_esg.id
}

resource aws_api_gateway_method method_post_holdings_with_path {
  authorization = "NONE"
  http_method = "POST"
  resource_id = aws_api_gateway_resource.holdings_source.id
  rest_api_id = aws_api_gateway_rest_api.data_agent_esg.id
}

resource aws_api_gateway_integration integration_holdings_source_with_path {
  rest_api_id             = aws_api_gateway_rest_api.data_agent_esg.id
  resource_id             = aws_api_gateway_resource.holdings_source.id
  http_method             = aws_api_gateway_method.method_post_holdings_with_path.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = aws_lambda_function.lambda.invoke_arn
}

resource aws_lambda_permission apigw_lambda_holdings_with_path {
  statement_id  = "AllowExecutionFromAPIGatewayHoldingsWithPath"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.lambda.function_name
  principal     = "apigateway.amazonaws.com"

  source_arn = "arn:aws:execute-api:${local.region}:${module.common-caller.account_id}:${aws_api_gateway_rest_api.data_agent_esg.id}/*/${aws_api_gateway_method.method_post_holdings_with_path.http_method}${aws_api_gateway_resource.holdings_source.path}"
}

#for method post holding -X POST (this will be removed)
resource aws_api_gateway_method method_post_holding {
  rest_api_id   = aws_api_gateway_rest_api.data_agent_esg.id
  resource_id   = aws_api_gateway_rest_api.data_agent_esg.root_resource_id
  http_method   = "POST"
  authorization = "NONE"
}

resource aws_api_gateway_integration integration_holding_source {
  rest_api_id             = aws_api_gateway_rest_api.data_agent_esg.id
  resource_id             = aws_api_gateway_rest_api.data_agent_esg.root_resource_id
  http_method             = aws_api_gateway_method.method_post_holding.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = aws_lambda_function.lambda.invoke_arn
}

resource aws_lambda_permission apigw_lambda_holdings {
  statement_id  = "AllowExecutionFromAPIGatewayHoldings"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.lambda.function_name
  principal     = "apigateway.amazonaws.com"

  source_arn = "arn:aws:execute-api:${local.region}:${module.common-caller.account_id}:${aws_api_gateway_rest_api.data_agent_esg.id}/*/${aws_api_gateway_method.method_post_holding.http_method}/"
}

#for method get calc result -X GET
resource aws_api_gateway_method method_get_result {
  rest_api_id   = aws_api_gateway_rest_api.data_agent_esg.id
  resource_id   = aws_api_gateway_rest_api.data_agent_esg.root_resource_id
  http_method   = "GET"
  authorization = "NONE"
}

resource aws_api_gateway_integration integration_result_source {
  rest_api_id             = aws_api_gateway_rest_api.data_agent_esg.id
  resource_id             = aws_api_gateway_rest_api.data_agent_esg.root_resource_id
  http_method             = aws_api_gateway_method.method_get_result.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = aws_lambda_function.lambda.invoke_arn
}

resource aws_lambda_permission apigw_lambda_result {
  statement_id  = "AllowExecutionFromAPIGatewayResult"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.lambda.function_name
  principal     = "apigateway.amazonaws.com"

  source_arn = "arn:aws:execute-api:${local.region}:${module.common-caller.account_id}:${aws_api_gateway_rest_api.data_agent_esg.id}/*/${aws_api_gateway_method.method_get_result.http_method}/"
}

#for method get-ondemand-holdings -X GET
resource aws_api_gateway_resource get_ondemand_holdings_source {
  path_part   = "get-ondemand-holdings"
  parent_id   = aws_api_gateway_rest_api.data_agent_esg.root_resource_id
  rest_api_id = aws_api_gateway_rest_api.data_agent_esg.id
}

resource aws_api_gateway_method get_ondemand_holdings {
  rest_api_id   = aws_api_gateway_rest_api.data_agent_esg.id
  resource_id   = aws_api_gateway_resource.get_ondemand_holdings_source.id
  http_method   = "GET"
  authorization = "NONE"
}

resource aws_api_gateway_integration integration_ondemand_holdings_source {
  rest_api_id             = aws_api_gateway_rest_api.data_agent_esg.id
  resource_id             = aws_api_gateway_resource.get_ondemand_holdings_source.id
  http_method             = aws_api_gateway_method.get_ondemand_holdings.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = aws_lambda_function.lambda.invoke_arn
}

resource aws_lambda_permission apigw_lambda_get_ondemand_holdings {
  statement_id  = "AllowExecutionFromAPIGatewayGetOndemandHoldings"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.lambda.function_name
  principal     = "apigateway.amazonaws.com"

  source_arn = "arn:aws:execute-api:${local.region}:${module.common-caller.account_id}:${aws_api_gateway_rest_api.data_agent_esg.id}/*/${aws_api_gateway_method.get_ondemand_holdings.http_method}${aws_api_gateway_resource.get_ondemand_holdings_source.path}"
}

#for method compare with ondemand holdings -X POST
resource aws_api_gateway_resource compare_with_ondemand_source {
  path_part   = "compare-with-ondemand-holdings"
  parent_id   = aws_api_gateway_rest_api.data_agent_esg.root_resource_id
  rest_api_id = aws_api_gateway_rest_api.data_agent_esg.id
}

resource aws_api_gateway_method method_post_compare_with_ondemand {
  rest_api_id   = aws_api_gateway_rest_api.data_agent_esg.id
  resource_id   = aws_api_gateway_resource.compare_with_ondemand_source.id
  http_method   = "POST"
  authorization = "NONE"
}

resource aws_api_gateway_integration integration_compare_with_ondemand_source {
  rest_api_id             = aws_api_gateway_rest_api.data_agent_esg.id
  resource_id             = aws_api_gateway_resource.compare_with_ondemand_source.id
  http_method             = aws_api_gateway_method.method_post_compare_with_ondemand.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = aws_lambda_function.lambda.invoke_arn
}

resource aws_lambda_permission apigw_lambda_compare_with_ondemand {
  statement_id  = "AllowExecutionFromAPIGatewayCompareWithOndemand"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.lambda.function_name
  principal     = "apigateway.amazonaws.com"

  source_arn = "arn:aws:execute-api:${local.region}:${module.common-caller.account_id}:${aws_api_gateway_rest_api.data_agent_esg.id}/*/${aws_api_gateway_method.method_post_compare_with_ondemand.http_method}${aws_api_gateway_resource.compare_with_ondemand_source.path}"
}

#for method get-version -X GET
resource aws_api_gateway_resource get_version_source {
  path_part   = "version"
  parent_id   = aws_api_gateway_rest_api.data_agent_esg.root_resource_id
  rest_api_id = aws_api_gateway_rest_api.data_agent_esg.id
}

resource aws_api_gateway_method get_version {
  rest_api_id   = aws_api_gateway_rest_api.data_agent_esg.id
  resource_id   = aws_api_gateway_resource.get_version_source.id
  http_method   = "GET"
  authorization = "NONE"
}

resource aws_api_gateway_integration integration_version_source {
  rest_api_id             = aws_api_gateway_rest_api.data_agent_esg.id
  resource_id             = aws_api_gateway_resource.get_version_source.id
  http_method             = aws_api_gateway_method.get_version.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = aws_lambda_function.lambda.invoke_arn
}

resource aws_lambda_permission apigw_lambda_get_version {
  statement_id  = "AllowExecutionFromAPIGatewayGetVersion"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.lambda.function_name
  principal     = "apigateway.amazonaws.com"

  source_arn = "arn:aws:execute-api:${local.region}:${module.common-caller.account_id}:${aws_api_gateway_rest_api.data_agent_esg.id}/*/${aws_api_gateway_method.get_version.http_method}${aws_api_gateway_resource.get_version_source.path}"
}

#for method get-s3-signed-url -X GET
resource aws_api_gateway_resource get_s3_signed_url_source {
  path_part   = "get-s3-signed-url"
  parent_id   = aws_api_gateway_rest_api.data_agent_esg.root_resource_id
  rest_api_id = aws_api_gateway_rest_api.data_agent_esg.id
}

resource aws_api_gateway_method get_s3_signed_url {
  rest_api_id   = aws_api_gateway_rest_api.data_agent_esg.id
  resource_id   = aws_api_gateway_resource.get_s3_signed_url_source.id
  http_method   = "GET"
  authorization = "NONE"
}

resource aws_api_gateway_integration integration_get_s3_signed_url_source {
  rest_api_id             = aws_api_gateway_rest_api.data_agent_esg.id
  resource_id             = aws_api_gateway_resource.get_s3_signed_url_source.id
  http_method             = aws_api_gateway_method.get_s3_signed_url.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = aws_lambda_function.lambda.invoke_arn
}

resource aws_lambda_permission apigw_lambda_get_s3_signed_url {
  statement_id  = "AllowExecutionFromAPIGatewayGetS3signedURL"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.lambda.function_name
  principal     = "apigateway.amazonaws.com"

  source_arn = "arn:aws:execute-api:${local.region}:${module.common-caller.account_id}:${aws_api_gateway_rest_api.data_agent_esg.id}/*/${aws_api_gateway_method.get_s3_signed_url.http_method}${aws_api_gateway_resource.get_s3_signed_url_source.path}"
}

#for method delete -X DELETE
resource aws_api_gateway_resource delete_job_source {
  path_part   = "jobs"
  parent_id   = aws_api_gateway_rest_api.data_agent_esg.root_resource_id
  rest_api_id = aws_api_gateway_rest_api.data_agent_esg.id
}

resource aws_api_gateway_method method_delete {
  rest_api_id = aws_api_gateway_rest_api.data_agent_esg.id
  resource_id = aws_api_gateway_resource.delete_job_source.id
  http_method = "DELETE"
  authorization = "NONE"
}

resource aws_api_gateway_integration integration_delete_method {
  rest_api_id = aws_api_gateway_rest_api.data_agent_esg.id
  resource_id = aws_api_gateway_resource.delete_job_source.id
  http_method = aws_api_gateway_method.method_delete.http_method
  integration_http_method = "POST"
  type = "AWS_PROXY"
  uri = aws_lambda_function.lambda.invoke_arn
}
resource "aws_lambda_permission" "apigw_lambda_delete" {
  statement_id = "AllowExecutionFromAPIGatewayDeleteJobs"
  action = "lambda:InvokeFunction"
  function_name = aws_lambda_function.lambda.function_name
  principal = "apigateway.amazonaws.com"

  source_arn = "arn:aws:execute-api:${local.region}:${module.common-caller.account_id}:${aws_api_gateway_rest_api.data_agent_esg.id}/*/${aws_api_gateway_method.method_delete.http_method}${aws_api_gateway_resource.delete_job_source.path}"
}

#deploy api-gateway
resource aws_api_gateway_deployment "latest" {
  rest_api_id = aws_api_gateway_rest_api.data_agent_esg.id
  stage_name = module.common-prefix.env
  description = "Deployed at ${timestamp()}"
  stage_description = <<DESCRIPTION
      ${aws_lambda_function.lambda.id},
      ${aws_lambda_permission.apigw_lambda_compare_with_ondemand.id},
      ${aws_lambda_permission.apigw_lambda_get_ondemand_holdings.id},
      ${aws_lambda_permission.apigw_lambda_get_version.id},
      ${aws_lambda_permission.apigw_lambda_get_s3_signed_url.id},
      ${aws_lambda_permission.apigw_lambda_holdings_with_path.id},
      ${aws_lambda_permission.apigw_lambda_delete.id}
  DESCRIPTION

  depends_on = [
    aws_api_gateway_integration.integration_trigger_source,
    aws_api_gateway_integration.integration_holding_source,
    aws_api_gateway_integration.integration_result_source,
    aws_api_gateway_integration.integration_ondemand_holdings_source,
    aws_api_gateway_integration.integration_compare_with_ondemand_source,
    aws_api_gateway_integration.integration_version_source,
    aws_api_gateway_integration.integration_get_s3_signed_url_source,
    aws_api_gateway_integration.integration_holdings_source_with_path,
    aws_api_gateway_integration.integration_delete_method
  ]

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_lambda_function" "lambda" {
  function_name = "${module.common-prefix.env-}data-agent-esg-lambda"
  handler = "com.morningstar.lambda.handler.ApiGatewayHandler"
  role = module.common-iam.arn
  s3_bucket = data.aws_s3_bucket_object.lambda.bucket
  s3_key = data.aws_s3_bucket_object.lambda.key
  s3_object_version = data.aws_s3_bucket_object.lambda.version_id
  runtime = "java8"
  memory_size = 512
  timeout = 50

  depends_on = [
    module.common-iam.is_permission_ready
  ]

  environment {
    variables = {
      mstar_environment = module.common-prefix.env
    }
  }
  tags = module.esg-tags.default_lambda_tags

  vpc_config {
    security_group_ids = distinct(module.common-vpc-sg.default_lb_security_group)
    subnet_ids = module.common-vpc.default_private_subnet_ids
  }

}

data aws_s3_bucket_object lambda {
  bucket = local.s3_lambda_bucket
  key = "${module.common-prefix.env}/${var.ecs_tag_version}/portfolio-data-agent-esg-lambda.jar"
}

locals {
  region = !module.common-prefix.isDR ? "us-east-1" : "us-west-2"
  s3_lambda_bucket = module.common-prefix.isPROD || module.common-prefix.isUAT? local._prod_s3_lambda_bucket : local._non_prod_s3_lambda_bucket
  _non_prod_s3_lambda_bucket = module.common-prefix.isNonProdDR?var.dr_nonprod_s3_lambda_bucket : var.nonprod_s3_lambda_bucket
  _prod_s3_lambda_bucket = module.common-prefix.isDR? var.dr_s3_lambda_bucket : var.prod_s3_lambda_bucket
}

variable "ecs_tag_version" {}

variable prod_s3_lambda_bucket {
  default = "prod-codedeploy-data-agent"
}

variable dr_s3_lambda_bucket {
  default = "dr-codedeploy-data-agent"
}

variable nonprod_s3_lambda_bucket {
  default = "nonprod-codedeploy-data-agent"
}

variable dr_nonprod_s3_lambda_bucket {
  default = "dr-nonprod-codedeploy-data-agent"
}