resource aws_lb_target_group lb-group-portfolio-service {
  name = "${module.common-prefix.env-}portofolio-service-group"
  port = 80
  target_type = "ip"
  protocol = "TCP"
  vpc_id = module.common-vpc.default_vpc

  health_check {
    interval = 30
    port = 80
    protocol = "TCP"
    healthy_threshold = 3
    unhealthy_threshold = 3
  }

  stickiness {
    enabled = false
    type = "source_ip"
  }

  tags = module.pdop-tags.default_lb_target_group_tags
}
