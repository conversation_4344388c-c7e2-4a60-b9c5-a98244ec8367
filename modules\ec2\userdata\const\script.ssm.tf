output ssm_agent {
  value = <<EOF
#!/usr/bin/env bash

yum install -y amazon-ssm-agent
start amazon-ssm-agent

EOF
}

output systemd_ssm_agent {
  value = <<EOF
#!/usr/bin/env bash

yum install -y amazon-ssm-agent
systemctl enable amazon-ssm-agent
systemctl start amazon-ssm-agent

EOF
}

output dnf_systemd_ssm_agent {
  value = <<EOF
#!/usr/bin/env bash

dnf install -y amazon-ssm-agent
systemctl enable amazon-ssm-agent
systemctl start amazon-ssm-agent

EOF
}
