variable mstar_env {
}

variable prefix {
  default = "sample"
}

locals {
  env = length(trimspace(var.mstar_env)) == 0? "TEST": replace(trimspace(var.mstar_env), "_", "-")
  prefix = length(trimspace(var.prefix)) == 0? "sample": trimspace(var.prefix)
  env- = upper(local.env) == "PROD"? "": format("%s-", local.env)
  _-env = upper(local.env) == "PROD"? "": format("-%s", local.env)
  env_ = replace(local.env-, "-", "_")
  _env = replace(local._-env, "-", "_")
  ENV_prefix = replace(format("%s%s", upper(local.env_), local.prefix), "-", "_")
  env_prefix = replace(format("%s%s", lower(local.env_), local.prefix), "-", "_")
  base-env = upper(local.env) == "DR"? "prod": replace(lower(local.env), "-dr", "")
  base-env- = upper(local.base-env) == "PROD"? "": format("%s-", local.base-env)
  base-env_ = replace(local.base-env-, "-", "_")
}

output env {
  value = lower(local.env)
}

output ENV {
  value = upper(local.env)
}

output env_ {
  value = lower(local.env_)
}

output env- {
  value = lower(local.env-)
}

output ENV_ {
  value = upper(local.env_)
}

output ENV- {
  value = upper(local.env-)
}

output _env {
  value = lower(local._env)
}

output _-env {
  value = lower(local._-env)
}

output _ENV {
  value = upper(local._env)
}

output _-ENV {
  value = upper(local._-env)
}

output prefix {
  value = local.prefix
}

output ENV_prefix {
  value = local.ENV_prefix
}

output ENV_prefix_ {
  value = "${local.ENV_prefix}_"
}

output env_prefix {
  value = local.env_prefix
}

output env_prefix_ {
  value = "${local.env_prefix}_"
}

output ENV-prefix {
  value = replace(local.ENV_prefix, "_", "-")
}

output ENV-prefix- {
  value = "${replace(local.ENV_prefix, "_", "-")}-"
}

output env-prefix {
  value = replace(local.env_prefix, "_", "-")
}

output env-prefix- {
  value = "${replace(local.env_prefix, "_", "-")}-"
}

output isDR {
  value = replace(upper(local.env), "DR", "") != upper(local.env)
}

output base-env {
  value = local.base-env
}

output base-env- {
  value = local.base-env-
}

output base-env_ {
  value = local.base-env_
}

output isPROD {
  value = local.base-env == "prod"
}

output isUAT {
  value = local.base-env == "uat"
}

output isNonProdDR {
  value = lower(local.env) == "qa-dr"
}
