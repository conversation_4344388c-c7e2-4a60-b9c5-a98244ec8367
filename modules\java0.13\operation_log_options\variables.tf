module common-prefix {
  source = "../../prefix"
  mstar_env = var.mstar_env
}

variable mstar_env {
}

variable name {}

variable aws_service_category {
  default = "ecs"
  description = "valid values are ecs, batch, lambda, etc."
}

variable log_group_name {
  default = "operations"
}

locals {
  log_group = "/aws/${length(var.aws_service_category) > 0? format("%s/", var.aws_service_category): var.aws_service_category}${module.common-prefix.env-}${var.log_group_name}"
  java-options = [
    "-DOperationLogGroup=${local.log_group}",
    "-DOperationLogStream=${module.common-prefix.env-}${var.name}",
  ]
}

output java-options {
  value = local.java-options
}

output operation_log_group {
  value = local.log_group
}
