module common-prefix {
  source = "../prefix"
  mstar_env = var.mstar_env
}

variable mstar_env {
}

data aws_s3_bucket bucket {
  bucket = "${module.common-prefix.env-}${local.name}"
}

variable name {
}

variable name_prefix {
  default = "mstar-dwm-"
}

variable catchall_name {
  default = "mstar-dwm-catchall"
}

output arn_prefix {
  value = {
    arn = data.aws_s3_bucket.bucket.arn
    prefix = length(local.prefix) > 0 && var.add_trailing_slash_to_prefix? format("%s/", local.prefix): local.prefix
  }
}

output arn {
  value = data.aws_s3_bucket.bucket.arn
}

output id {
  value = data.aws_s3_bucket.bucket.id
}

output prefix {
  value = length(local.prefix) > 0 && var.add_trailing_slash_to_prefix? format("%s/", local.prefix): local.prefix
}

variable prefix {
  default = ""
}

variable add_trailing_slash_to_prefix {
  default = true
}

locals {
  catchall_enabled = length(var.catchall_name) > 0 && (module.common-prefix.ENV == "QA" || module.common-prefix.ENV == "DEV")
  _name = format("%s%s", var.name_prefix, var.name)
  name = local.catchall_enabled? var.catchall_name: local._name
  _prefix = length(trimspace(var.prefix)) > 0? replace(replace(trimspace(var.prefix), "/^/*/", ""), "//*$/", ""): ""
  prefix = "${local.catchall_enabled? var.name: ""}${local.catchall_enabled && length(local._prefix)>0? format("/%s", local._prefix): local._prefix}"
}
