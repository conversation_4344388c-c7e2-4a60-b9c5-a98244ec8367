#!/usr/bin/env bash

# cat <<'EOF' > /tmp/base.json
# {
#   "tid" : "do",
#   "environment": "${base-env}",
#   "function": "${function}",
#   "domain_join": "true"
# }
# EOF
mkdir /mstar-metadata
# limited by the domain-join script as below (from SSM document), the JSON content has to be in one line without space
#   "TID=`awk -F \",\" '{print $1}' /mstar-metadata/base.json |awk -F \":\" '{print $2}'|tr -d '\"'`  #Get TID",
#   "ENV=`awk -F \",\" '{print $2}' /mstar-metadata/base.json |awk -F \":\" '{print $2}'|tr -d '\"'` #Get ENV",
echo "{\"tid\":\"${tid}\",\"environment\":\"${base-env}\",\"function\":\"${function}\",\"domain_join\":\"true\"}" > /mstar-metadata/base.json

systemctl enable mstar-chef-init
systemctl restart mstar-chef-init
chef-client --once -r 'recipe[mstar-authentication::join]'  -c /var/mstar/chef/etc/chef-client.rb
chef-client --once -r 'recipe[mstar-authentication::groups]'  -c /var/mstar/chef/etc/chef-client.rb

