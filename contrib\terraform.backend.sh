#!/usr/bin/env bash
TOP_LEVEL_PATH=$(git rev-parse --show-toplevel)
INPUT=${TOP_LEVEL_PATH}/contrib/terraform.backend.tf
OUTPUT=terraform.backend.auto.tf
echo ------- create ${OUTPUT} -------
cat ${INPUT} | \
  sed "s/@TF_BUCKET@/${TF_BUCKET}/" | \
  sed "s/@TF_REGION@/${TF_REGION:-${AWS_REGION:-us-east-1}}/" | \
  sed "s,@PATH_PREFIX@,$(${TOP_LEVEL_PATH}/contrib/generate-prefix.sh)," | \
  tee ${OUTPUT}
echo
