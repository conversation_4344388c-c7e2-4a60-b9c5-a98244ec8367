#!/usr/bin/env sh

if [ $#  -lt 1 ];then
  echo "usage:$0 plan|apply"
  exit 33
fi

case $1 in
"plan")
  TF_ACTION_PLAN=yes
  ;;
"apply")
  TF_ACTION_APPLY=yes
  ;;
"plan-and-apply")
  TF_ACTION_PLAN=yes
  TF_ACTION_APPLY=yes
  ;;
"plan-destroy")
  TF_ACTION_PLAN_DESTROY=yes
  ;;
"destroy")
  TF_ACTION_DESTROY=yes
  ;;
*)
  echo "usage:$0 plan|apply|plan-and-apply"
  exit 33 ;;
esac

export MSTAR_ENV=${MSTAR_ENV:-test}
MSTAR_ENV_U=$(echo "${MSTAR_ENV}" | tr 'a-z' 'A-Z')
MSTAR_ENV_L=$(echo "${MSTAR_ENV}" | tr 'A-Z' 'a-z')
export AWS_DEFAULT_REGION="${AWS_DEFAULT_REGION:-us-east-1}"

export AWS_DR_REGION="${AWS_DR_REGION:-us-west-2}"
if [ x"${MSTAR_ENV_U}" = "xDR" ] || [ x"${MSTAR_ENV_U}" = "xSTG-DR" ] || [ x"${MSTAR_ENV_U}" = "xDEV-DR" ] || [ x"${MSTAR_ENV_U}" = "xQA-DR" ]; then
  export AWS_REGION="${AWS_DR_REGION}"
else
  export AWS_REGION="${AWS_DEFAULT_REGION}"
fi

case x"${MSTAR_ENV_U}" in
  xPROD)
    export TF_BUCKET="${PROD_TF_BUCKET:-do-terraform-state-prod}"
  ;;
  xUAT)
    export TF_BUCKET="${PROD_TF_BUCKET:-do-terraform-state-prod}"
  ;;
  xDR)
    export TF_BUCKET="${PROD_TF_BUCKET:-dr-do-terraform-state-prod}"
  ;;
  *-DR)
    export TF_BUCKET="${NONPROD_TF_BUCKET:-dr-do-terraform-state-nonprod}"
  ;;
  *)
    export TF_BUCKET="${NONPROD_TF_BUCKET:-do-terraform-state-nonprod}"
  ;;
esac

TF_ARGS=""
(cd .. && find . -type f \( -name \*.auto.tfvars -o -name \*.auto.tf \) -exec rm "{}" \;)
(cd .. && find . -type d -name .terraform -exec rm -r "{}" \;)

generate_script(){
  GENERATOR_FILE="./generate.sh"
  [ -f "${GENERATOR_FILE}" ] && . "${GENERATOR_FILE}" && pwd
  [ x"${ENV_NEUTRAL_SCRIPTS}" != "x" ] && cd "${ENV_NEUTRAL_SCRIPTS}" && [ -f "${GENERATOR_FILE}" ] && . "${GENERATOR_FILE}" && pwd
  PROVIDER_AWS_FILE_out="provider.aws.auto.tf"
  # to make sure there is only one that has no local modifications
  cat > "${PROVIDER_AWS_FILE_out}" <<EOF
provider "aws" {
  version = "~> 3.0"
  region = "${AWS_REGION}"
}

provider "aws" {
  version = "~> 3.0"
  alias = "s3default"
  region = "${AWS_DEFAULT_REGION}"
}

provider "aws" {
  version = "~> 3.0"
  alias = "replication"
  region = "${AWS_DR_REGION}"
}
EOF
}

set_backend(){
  TF_BACKEND_FILE_in="../../contrib/terraform.backend.tf"
  TF_BACKEND_FILE_out="terraform.backend.auto.tf"
  [ x"${S3_BACKEND_PATH_PREFIX}" != "x" ] && PATH_PREFIX="${S3_BACKEND_PATH_PREFIX}" || PATH_PREFIX="${AWS_REGION}/${MSTAR_ENV_L}/${MODULE#[[:digit:]]*-}"
  [ ! -f "terraform.backend.tf" ] && echo "create ${TF_BACKEND_FILE_out}" && cat "${TF_BACKEND_FILE_in}" |\
	sed "s/@TF_BUCKET@/${TF_BUCKET}/" | \
	sed "s/@TF_REGION@/${TF_REGION:-${AWS_REGION}}/" | \
	sed "s,@AWS_REGION@/@PATH_PREFIX@,${PATH_PREFIX}," | \
	tee "${TF_BACKEND_FILE_out}" || :
}

set_default(){
  TF_VAR_FILE_in="../../contrib/terraform.${MSTAR_ENV_U}.tfvars"
  TF_VAR_FILE_out="terraform.auto.tfvars"
  [ -f "${TF_VAR_FILE_in}" ] && echo "create ${TF_VAR_FILE_out}" && cat "${TF_VAR_FILE_in}" | \
	tee "${TF_VAR_FILE_out}" || :
}

tf_init_and_action(){
  terraform init -no-color -backend=true -force-copy -verify-plugins=true
  TF_PROFILE=$(echo "${TF_PROFILE}" | tr 'a-z' 'A-Z')
  for TF_VAR_FILE in "terraform.${MSTAR_ENV_U}-${TF_PROFILE}.tfvars" "terraform.${TF_PROFILE}.tfvars" "terraform.${MSTAR_ENV_U}.tfvars"; do
    [ -f "${TF_VAR_FILE}" ] && echo "found ${TF_VAR_FILE}" && TF_ARGS="${TF_ARGS} -var-file ${TF_VAR_FILE}" && break
  done
  [ x"${TF_ACTION_PLAN:-no}" = "xyes" ] && ( terraform plan -no-color ${TF_ARGS} || exit 2 )
  [ x"${TF_ACTION_PLAN_DESTROY:-no}" = "xyes" ] && ( terraform plan -no-color -destroy ${TF_ARGS} || exit 2 )
  V=$( terraform version | head -n 1 ); H=$( (echo Terraform v0.11; echo $V) | sort -t. | tail -n 1 ); [ x"${V}" = x"${H}" ] && TF_ARGS="${TF_ARGS} -auto-approve"
  [ x"${TF_ACTION_APPLY:-no}" = "xyes" ] && ( terraform apply -no-color ${TF_ARGS} || exit 2 )
  [ x"${TF_ACTION_DESTROY:-no}" = "xyes" ] && ( terraform destroy -no-color ${TF_ARGS} || exit 2 )
}

wrapper(){
  MODULE="$1"
  (
	echo ------- begin ${MODULE} -------
    [ -d "../shared/${MODULE}" ] && cd "../shared/${MODULE}" || cd ${MODULE} || exit 1
	generate_script
	[ x"${SKIP_THIS_MODULE}" = "xyes" ] && echo "skip module ${MODULE}" && exit
	[ x"${ENABLE_S3_BACKEND:-yes}" = "xyes" ] && set_backend
	set_default
	tf_init_and_action
	echo ------- end ${MODULE} -------
  ) || exit 2
}

shift
echo "module folders: $@"
for i in ${@}; do
  MODULE=$(echo $i | sed 's,/*$,,')
  if [ x"${DEBUG_SCRIPT:-no}" = "xyes" ]; then
	( wrapper $MODULE )
  else
	wrapper $MODULE
  fi
done
