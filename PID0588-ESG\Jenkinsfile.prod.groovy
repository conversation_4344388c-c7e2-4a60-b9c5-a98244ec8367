#!groovy

pipeline {
    parameters {
        choice(choices: 'PROD\nDR', description: 'deploy PROD or UAT?', name: 'deploy_env')
        string(name: 'GIT_REPO', defaultValue: 'https://msstash.morningstar.com/scm/do/pms.aws-service.script.git', description: 'the repo to fetch code')
        string(name: 'GIT_BRANCH', defaultValue: 'master', description: 'the branch to build, for example "release/1.21"')
        choice(choices: 'us-east-1\neu-west-1\nap-northeast-1', description: 'What AWS region?', name: 'aws_region')
        choice(choices: 'us-west-2', description: 'DR region?', name: 'dr_region')
        string(name: 'ECS_TAG_VERSION', defaultValue: '', description: 'Select the docker image tag to be deployed if not latest')
        choice(choices: 'PID0588-ESG\nPID0610-PDOP\nPID0006-DCS', name: 'TF_BASE', description: 'root module collections')
        string(name: 'TF_MODULES', defaultValue: '', description: 'Select which module to deploy and skip all others')
        choice(choices: '\nNOP\nbatch-teardown', name: 'TF_PROFILE', description: 'override settings with disabled state, set count to 0 or prevent from creating resources')
        choice(choices: 'plan-and-apply\nplan\napply\ndestroy', description: 'What to do?', name: 'TF_ACTION')
    }

    environment {
        NONPROD_ACCOUNT = '************'
        NONPROD_ROLE = 'DO-ENGR-Deploy'
        NONPROD_TF_BUCKET = "${params.deploy_env =~ 'DR' ? 'dr-do-terraform-state-nonprod' : 'do-terraform-state-nonprod'}"
        PROD_ACCOUNT = '************'
        PROD_ROLE = 'DO-ENGR-Deploy'
        PROD_TF_BUCKET = "${params.deploy_env =~ 'DR' ? 'dr-do-terraform-state-prod' : 'do-terraform-state-prod'}"
        AWS_DEFAULT_REGION = "${params.aws_region}"
        AWS_DR_REGION = "${params.dr_region}"
        TERRAFORM_IMAGE = "hashicorp/terraform:0.12.31"
        TF_CONFIRM_APPLY = "yes"
        TF_PROFILE = "${params.TF_PROFILE == 'batch-teardown' ? 'NOP' : params.TF_PROFILE}"
    }

    options {
        ansiColor('xterm')
        skipDefaultCheckout()
        skipStagesAfterUnstable()
    }

    agent none

    stages {
        stage('make sure STG/PROD resources are not torn down') {
            when {
                expression {
                    params.TF_PROFILE == "NOP" && (params.deploy_env == "STG" || params.deploy_env == "PROD")
                }
            }
            agent none
            steps {
                error('cannot tear down STG/PROD resources')
            }
        }

        /* Download from stash */
        stage('download source code') {
            agent { label 'linux' }
            steps {
                // cleanWs
                deleteDir()
                echo 'download source code ...'
                git credentialsId: '8d2dd645-3268-4220-bdae-14d5be0ae0eb', url: '${GIT_REPO}', branch: '${GIT_BRANCH}'
                stash includes: '**/*', name: 'tf'
            }
        }

        stage('plan to destroy') {
            when {
                expression {
                    TF_ACTION.matches("(.*)destroy")
                }
            }
            agent { label 'awsjenklinux' }
            steps {
                sh 'aws --version'
                deleteDir()
                unstash 'tf'
                sh '''
TF_ACTION=plan-destroy
set +x
AWS_CREDENTIALS=`mktemp`
if [ "x${deploy_env^^}" = "xPROD" ] || [ "x${deploy_env^^}" = "xDR" ] || [ "x${deploy_env^^}" = "xUAT" ]; then
 ( source ./assume_role.sh ${PROD_ACCOUNT} ${PROD_ROLE} ${AWS_CREDENTIALS} && aws s3 ls ${PROD_TF_BUCKET} --recursive || { echo "cannot access state file"; exit 22; } )
else
 ( source ./assume_role.sh ${NONPROD_ACCOUNT} ${NONPROD_ROLE} ${AWS_CREDENTIALS} && aws s3 ls ${NONPROD_TF_BUCKET} --recursive || { echo "cannot access state file"; exit 22; } )
fi
docker run \\
  --rm \\
  -v "$(pwd)":/deploy:ro \\
  -v ${AWS_CREDENTIALS}:/aws_credentials:ro \\
  --entrypoint sh \\
  --tmpfs /tmp:exec \\
  -e TF_VAR_ecs_tag_version="${ECS_TAG_VERSION}" \\
  -e TF_MODULES="${TF_MODULES}" \\
  -e TF_PROFILE="${TF_PROFILE}" \\
  -e MSTAR_ENV="${deploy_env,,}" \\
  ${TERRAFORM_IMAGE} \\
  -c "cp -R /deploy /tmp && \\
    cd /tmp/deploy/${TF_BASE} && \\
    ( source /aws_credentials && sh ../contrib/terraform-wrapper-0.12.sh ${TF_ACTION} ${TF_MODULES} )"
'''
            }
        }

        stage('confirm destroy') {
            when {
                expression {
                    TF_ACTION.matches("(.*)destroy")
                }
            }
            agent none
            steps {
                script {
                    timeout(10) {
                        input(message: 'Should the plan result be correct, confirm destroy resources?')
                        env.confirmed_destroy = 'yes'
                    }
                }
            }
        }

        stage('apply destroy resources') {
            when {
                expression {
                    env.confirmed_destroy == 'yes'
                }
            }
            agent { label 'awsjenklinux' }
            steps {
                deleteDir()
                unstash 'tf'
                sh '''
TF_ACTION=destroy
# same
set +x
AWS_CREDENTIALS=`mktemp`
if [ "x${deploy_env^^}" = "xPROD" ] || [ "x${deploy_env^^}" = "xDR" ] || [ "x${deploy_env^^}" = "xUAT" ]; then
 ( source ./assume_role.sh ${PROD_ACCOUNT} ${PROD_ROLE} ${AWS_CREDENTIALS} && aws s3 ls ${PROD_TF_BUCKET} --recursive || { echo "cannot access state file"; exit 22; } )
else
 ( source ./assume_role.sh ${NONPROD_ACCOUNT} ${NONPROD_ROLE} ${AWS_CREDENTIALS} && aws s3 ls ${NONPROD_TF_BUCKET} --recursive || { echo "cannot access state file"; exit 22; } )
fi
docker run \\
  --rm \\
  -v "$(pwd)":/deploy:ro \\
  -v ${AWS_CREDENTIALS}:/aws_credentials:ro \\
  --entrypoint sh \\
  --tmpfs /tmp:exec \\
  -e TF_VAR_ecs_tag_version="${ECS_TAG_VERSION}" \\
  -e TF_MODULES="${TF_MODULES}" \\
  -e TF_PROFILE="${TF_PROFILE}" \\
  -e MSTAR_ENV="${deploy_env,,}" \\
  -e DEBUG_SCRIPT="${DEBUG_SCRIPT}" \\
  ${TERRAFORM_IMAGE} \\
  -c "cp -R /deploy /tmp && \\
    cd /tmp/deploy/${TF_BASE} && \\
    ( source /aws_credentials && sh ../contrib/terraform-wrapper-0.12.sh ${TF_ACTION} ${TF_MODULES} )"
'''
            }
        }


        stage('action') {
            agent { label 'awsjenklinux' }
            steps {
                sh 'aws --version'
                deleteDir()
                unstash 'tf'
                sh '''
[ "x${TF_CONFIRM_APPLY}" = "xyes" ] && TF_ACTION=plan
set +x
AWS_CREDENTIALS=`mktemp`
if [ "x${deploy_env^^}" = "xPROD" ] || [ "x${deploy_env^^}" = "xDR" ] || [ "x${deploy_env^^}" = "xUAT" ]; then
 ( source ./assume_role.sh ${PROD_ACCOUNT} ${PROD_ROLE} ${AWS_CREDENTIALS} && aws s3 ls ${PROD_TF_BUCKET} --recursive || { echo "cannot access state file"; exit 22; } )
else
 ( source ./assume_role.sh ${NONPROD_ACCOUNT} ${NONPROD_ROLE} ${AWS_CREDENTIALS} && aws s3 ls ${NONPROD_TF_BUCKET} --recursive || { echo "cannot access state file"; exit 22; } )
fi
docker run \\
  --rm \\
  -v "$(pwd)":/deploy:ro \\
  -v ${AWS_CREDENTIALS}:/aws_credentials:ro \\
  --entrypoint sh \\
  --tmpfs /tmp:exec \\
  -e TF_VAR_ecs_tag_version="${ECS_TAG_VERSION}" \\
  -e TF_MODULES="${TF_MODULES}" \\
  -e TF_PROFILE="${TF_PROFILE}" \\
  -e MSTAR_ENV="${deploy_env,,}" \\
  ${TERRAFORM_IMAGE} \\
  -c "cp -R /deploy /tmp && \\
    cd /tmp/deploy/${TF_BASE} && \\
    ( source /aws_credentials && sh ../contrib/terraform-wrapper-0.12.sh ${TF_ACTION} ${TF_MODULES} )"
'''
            }
        }

        stage('confirm apply') {
            when {
                expression {
                    TF_ACTION.matches("(.*)apply") && TF_CONFIRM_APPLY == 'yes'
                }
            }
            agent none
            steps {
                script {
                    timeout(10) {
                        input(message: 'Should the plan result be correct, confirm apply?')
                        env.confirmed_apply = 'yes'
                    }
                }
            }
        }

        stage('apply changes') {
            when {
                expression {
                    env.confirmed_apply == 'yes'
                }
            }
            agent { label 'awsjenklinux' }
            steps {
                deleteDir()
                unstash 'tf'
                sh '''
TF_ACTION=apply
# same
set +x
AWS_CREDENTIALS=`mktemp`
if [ "x${deploy_env^^}" = "xPROD" ] || [ "x${deploy_env^^}" = "xDR" ] || [ "x${deploy_env^^}" = "xUAT" ]; then
 ( source ./assume_role.sh ${PROD_ACCOUNT} ${PROD_ROLE} ${AWS_CREDENTIALS} && aws s3 ls ${PROD_TF_BUCKET} --recursive || { echo "cannot access state file"; exit 22; } )
else
 ( source ./assume_role.sh ${NONPROD_ACCOUNT} ${NONPROD_ROLE} ${AWS_CREDENTIALS} && aws s3 ls ${NONPROD_TF_BUCKET} --recursive || { echo "cannot access state file"; exit 22; } )
fi
docker run \\
  --rm \\
  -v "$(pwd)":/deploy:ro \\
  -v ${AWS_CREDENTIALS}:/aws_credentials:ro \\
  --entrypoint sh \\
  --tmpfs /tmp:exec \\
  -e TF_VAR_ecs_tag_version="${ECS_TAG_VERSION}" \\
  -e TF_MODULES="${TF_MODULES}" \\
  -e TF_PROFILE="${TF_PROFILE}" \\
  -e MSTAR_ENV="${deploy_env,,}" \\
  -e DEBUG_SCRIPT="${DEBUG_SCRIPT}" \\
  ${TERRAFORM_IMAGE} \\
  -c "cp -R /deploy /tmp && \\
    cd /tmp/deploy/${TF_BASE} && \\
    ( source /aws_credentials && sh ../contrib/terraform-wrapper-0.12.sh ${TF_ACTION} ${TF_MODULES} )"
'''
            }
        }

    }
}
