module common-prefix {
  source    = "../../prefix"
  mstar_env = var.mstar_env
  prefix    = var.name
}

variable mstar_env {}

variable name {}

variable name-prefix {
  type = string
  default = "dcs"
}

resource aws_cloudwatch_event_rule dcs_outposts_schedule {
  count               = "${ceil(length(var.instance_ids) / 5.0)}"
  name                = "${var.name-prefix}${format("-%d", count.index)}"
  description         = var.dcs_outposts_schedule_description
  schedule_expression = var.dcs_outposts_schedule
}

resource aws_cloudwatch_event_target schedule_dcs_instances {
  depends_on = [aws_cloudwatch_event_rule.dcs_outposts_schedule]
  count    = length(var.instance_ids)
  rule     = "${aws_cloudwatch_event_rule.dcs_outposts_schedule.*.name[floor(count.index/5)]}"
  arn      = var.target_arn
  role_arn = var.target_role_arn
  input    = var.instance_ids[count.index]
}


variable dcs_outposts_schedule {
  default = "cron(0 14 * * ? *)"
}

variable dcs_outposts_schedule_description {
  default = "stop dcs outposts servers 10PM(China time)"
}

variable target_arn {
  default = "arn:aws:events:us-east-1:146985796559:target/stop-instance"
}

variable target_role_arn {
  default = "arn:aws:iam::146985796559:role/AWS_Events_Actions_Execution"
}

variable instance_ids {

}
