#!/usr/bin/env bash

echo "\#(o_o)#/"

sudo su

echo "start to build TS-FCGI env:${base-env}">> /tmp/install.log

echo "update time zone...">> /tmp/install.log
export TZ=America/Chicago
ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

useradd
s3bucket="dcs-deploy-tempfolder"
if [[ "${base-env}" = "prod" ]] || [[ "${base-env}" = "uat" ]]; then
  s3bucket="dcs-deploy-prod-tempfolder"
fi

aws s3 cp s3://$s3bucket/TSCache/depends.tar.gz . 2>>/tmp/install.log
aws s3 cp s3://$s3bucket/TSCache/www.tar.gz . 2>>/tmp/install.log
aws s3 cp s3://$s3bucket/TSCache/apache2.tar.gz . 2>>/tmp/install.log

echo "downloaded depends...">> /tmp/install.log

##upzip##
tar -xzvf depends.tar.gz 2>>/tmp/install.log
tar -xzvf www.tar.gz 2>>/tmp/install.log
tar -xzvf apache2.tar.gz 2>>/tmp/install.log

##install##
#depends
mkdir -p /opt/mstar/storage/ 2>>/tmp/install.log
mv depends /opt/mstar/storage/ 2>>/tmp/install.log
rm -f depends.tar.gz

#apache
mkdir -p /data/logs/apache_log
mv apache2 /usr/local/ 2>>/tmp/install.log
rm -f apache2.tar.gz

#www & odbc
mv www/etc/odbc/* /usr/local/etc/ 2>>/tmp/install.log
mv www /data/ 2>>/tmp/install.log
groupadd apache
useradd apache -g apache
chown -R apache:apache /data/www 2>>/tmp/install.log
chown -R apache:apache /data/logs/apache_log 2>>/tmp/install.log
rm -f www.tar.gz

echo "/opt/mstar/storage/depends" >>/etc/ld.so.conf

ldconfig

echo "installed depends...">> /tmp/install.log
#####replace ip####
cd /data/www/etc 2>>/tmp/install.log

echo "start to replace ip">>/tmp/install.log
if [[ "${base-env}" = "PROD" ]]; then
  filelist=`aws s3 ls s3://dcs-deploy-prod-tempfolder/${base-env}_dcs_tscache_dcc_hostlist/ |awk '{print $4}'`
else
  filelist=`aws s3 ls s3://dcs-deploy-tempfolder/${base-env}_dcs_tscache_dcc_hostlist/ |awk '{print $4}'`
fi

if [[ -z "$filelist" ]];then
  echo "could not find filelist on s3">>/tmp/install.log
  exit
fi

config=""
for file in $filelist
do
  aws s3 cp s3://dcs-deploy-tempfolder/${base-env}_dcs_tscache_dcc_hostlist/$file . 2>>/tmp/install.log
  ip=`cat $file`
  echo "got $ip for $file">> /tmp/install.log
  config=$config$ip:11211/tcp","
  if [[ -e $file ]];then
    rm -f $file
  fi
done

#remove ,
config=`echo $config|awk '{print substr($0,1,length($0)-1)}'`

config="<server-parm productid=\"1000\" server-addr=\"$config\" timeout=\"30000\" threadnum=\"10\" maprefreshinterval=\"28800\" />"

awk '{if(index($0,"server-parm")){i++;if(i==1)print $0}else print $0;}' \
tscs_fcgi.xml |sed -e "s%<server-parm.*%$config%" >tscs_fcgi.xml.tmp
mv tscs_fcgi.xml.tmp tscs_fcgi.xml 2>>/tmp/install.log
echo "replaced $config">>/tmp/install.log


###update rc3,5###
cat>httpd<<EOF
#!/bin/bash
/usr/local/apache2/bin/apachectl -k start
EOF
chmod +x httpd
mv httpd /etc/init.d/httpd 2>>/tmp/install.log
cd /etc/rc3.d/ && ln -s ../init.d/httpd S88httpd && cd - 2>>/tmp/install.log
cd /etc/rc5.d/ && ln -s ../init.d/httpd S88httpd && cd - 2>>/tmp/install.log

echo "setup rc3,5">>/tmp/install.log

##add log for splunk##
#apache
mkdir -p /data/app_logs/apache_log 2>>/tmp/install.log
cd /data/app_logs/apache_log && ln -s  /data/logs/apache_log/error_log error_log&& cd - 2>>/tmp/install.log
#fcgi
mkdir -p /data/app_logs/www_log 2>>/tmp/install.log
cd /data/app_logs/www_log &&  ln -s /data/www/log/tscs_cgi.log tscs_cgi.log && cd - 2>>/tmp/install.log

echo "linked log">>/tmp/install.log

#start
/usr/local/apache2/bin/apachectl  -k start 2>>/tmp/install.log

echo "install done.">> /tmp/install.log
