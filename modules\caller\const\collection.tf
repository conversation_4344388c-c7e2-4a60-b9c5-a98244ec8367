variable collection_nonprod {
  default = 179020893316
}

variable collection_prod {
  default = 252621515393
}

output collection_prod {
  value = "arn:aws:iam::${var.collection_prod}:root"
}

output collection_nonprod {
  value = "arn:aws:iam::${var.collection_nonprod}:root"
}

output collection_prod_operator {
  value = "arn:aws:iam::${var.collection_prod}:role/mstar-dc-prod-operator"
}

output collection_nonprod_operator {
  value = "arn:aws:iam::${var.collection_nonprod}:role/mstar-dc-non-prod-operator"
}

output collection_prod_glue {
  value = "arn:aws:iam::${var.collection_prod}:role/redshift-glue-catalog-full-access"
}

output collection_nonprod_glue {
  value = "arn:aws:iam::${var.collection_nonprod}:role/redshift-glue-catalog-full-access"
}
