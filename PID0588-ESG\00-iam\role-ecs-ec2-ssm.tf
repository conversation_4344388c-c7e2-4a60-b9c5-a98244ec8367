resource "aws_iam_role" "ecsInstanceWithSSM" {
  count = var.enable_role_ecsInstanceRoleDO? 1:0
  name_prefix = "ecsInstanceWithSSM"
  path = "/service-role/"
  assume_role_policy = module.common-iam-const.assume-role-policy-ec2
  tags = module.common-tags.default_iam_role_tags
}

resource "aws_iam_role_policy_attachment" "ecsInstanceWithSSM-permissions" {
  count = var.enable_role_ecsInstanceRoleDO? length(module.common-iam-const.iam-policies-ecs-with-adclient-ssm):0
  role = aws_iam_role.ecsInstanceWithSSM[0].name
  policy_arn = module.common-iam-const.iam-policies-ecs-with-adclient-ssm[count.index]
  provisioner "local-exec" {
    command = "sleep 15"
  }
}

resource "aws_iam_instance_profile" "ecsInstanceWithSSM" {
  count = var.enable_role_ecsInstanceRoleDO? 1:0
  name = "ecsInstanceWithSSM"
  role = aws_iam_role.ecsInstanceWithSSM[0].name
}
