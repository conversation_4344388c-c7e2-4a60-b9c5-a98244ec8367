resource "aws_ecs_task_definition" "poc-ads-worker-dcs-task" {
  container_definitions = templatefile("templates/ecs.container_definition-calc.json", {
    name = "${module.common-prefix.env-}${local.name}"
    image = module.common-ecs-ecr.path
    mstar-env = module.common-prefix.env
    awslogs-group = module.common-ecs-logs.log_group
    awslogs-region = module.common-ecs-logs.log_region
    awslogs-stream-prefix = module.common-ecs-logs.log_prefix
  })
  family = module.common-prefix.env-prefix
  task_role_arn = module.common-iam.arn
  requires_compatibilities = [
    "EC2"
  ]
  network_mode = "awsvpc"
}

module "common-iam" {
  source = "../../modules/iam/resource"
  mstar_env = var.mstar_env
  name = "ads-dcs-worker-role"
  use_role_name_prefix = false
  service_prefix = ""
  policies = distinct(concat(module.common-iam-const.iam-policies-ecsTask, module.common-iam-const.iam-policies-elasticache-readonly))
  policy = var.policy
  iam_role_tags = module.pcs-tags.default_iam_role_tags
}

module "common-ecs-ecr" {
  source = "../../modules/ecs/ecr"
  repository = "do/dcs-images-repo"
  ecs_tag_version = var.ecs_tag_version
}

variable "ecs_tag_version" {}

variable policy {
  type = list(string)
  default = []
}

