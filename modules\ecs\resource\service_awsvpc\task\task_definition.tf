module common-prefix {
  source = "../../../../prefix"
  mstar_env = var.mstar_env
  prefix = var.service
}

variable mstar_env {}

variable cluster {}

variable service {}

module common-ecs-logs {
  source = "../../../logs"
  mstar_env = var.mstar_env
  name = var.cluster
  service_name = var.service
}

resource aws_ecs_task_definition task_ec2 {
  count = var.use_ec2? 1:0
  container_definitions = data.template_file.container_definition.rendered
  family = module.common-prefix.env-prefix
  task_role_arn = var.role
  network_mode = "awsvpc"
}

resource aws_ecs_task_definition task_fargate {
  count = var.use_ec2? 0:1
  container_definitions = data.template_file.container_definition.rendered
  family = module.common-prefix.env-prefix
  task_role_arn = var.role
  execution_role_arn = data.aws_iam_role.ecsTaskExecutionRole.arn
  requires_compatibilities = [
    "FARGATE",
  ]
  network_mode = "awsvpc"
  cpu = var.cpu
  memory = var.memory
  tags = var.ecs_task_tags
}

variable use_ec2 {
  default = false
}

variable ecs_task_tags {
  type = map(string)
}

data aws_iam_role ecsTaskExecutionRole {
  name = var.execution_role
}

variable execution_role {
  default = "ecsTaskExecutionRole"
}

locals {
  task_resource = var.use_ec2? aws_ecs_task_definition.task_ec2: aws_ecs_task_definition.task_fargate
  resource_arn = local.task_resource[0].arn
}

output arn {
  value = local.resource_arn
}

variable role {
}

variable cpu {
}

variable memory {
}

variable portMappings {
  type = list(any)
  default = []
}

variable ulimits {
  type = list(any)
  default = []
}

data template_file container_definition {
  template = replace(replace(file("${path.module}/templates/container_definition.json"), "\"BEGIN{}", ""), "END{}\"", "")
  vars = {
    name = module.common-prefix.env-prefix
    image = module.common-ecs-ecr.path
    environment = format("[%s]", join(",", formatlist("{\"name\":\"%v\", \"value\":\"%v\"}", keys(local.environment), values(local.environment))))
    awslogs-group = module.common-ecs-logs.log_group
    awslogs-region = module.common-ecs-logs.log_region
    awslogs-stream-prefix = module.common-ecs-logs.log_prefix
    cpu = var.cpu
    memoryReservation = var.memory
    portMappings = replace(jsonencode(var.portMappings), "/\"(true|false|[[:digit:]]+)\"/", "$1")
    ulimits = replace(jsonencode(var.ulimits), "/\"(true|false|[[:digit:]]+)\"/", "$1")
    linuxParameters = var.use_ec2? replace(jsonencode(local.linux-parameters), "/\"(true|false|[1-9][[:digit:]]*)\"/", "$1"): "{}"
  }
}

locals {
  linux-parameters = {
    tmpfs = [
      {
        containerPath = "/tmp/kc"
        mode = "0600"
        size = 1
      }
    ]
  }
}

variable repository {
}

module common-ecs-ecr {
  source = "../../../ecr"
  repository = var.repository
  ecs_tag_version = var.ecs_tag_version
}

variable ecs_tag_version {}

variable java_options {
  type = list(string)
}

variable environment {
  type = map(string)
  default = {}
}

locals {
  default_environment = {
    MSTAR_ENV = module.common-prefix.env
    JAVA_TOOL_OPTIONS = join(" ", distinct(var.java_options))
  }
  environment = merge(local.default_environment, var.environment)
}
